<template>
  <div class="app-container">
    <!-- 匹配类型选择 -->
    <el-card class="match-type-card" shadow="never">
      <el-radio-group v-model="matchType" @change="handleMatchTypeChange" size="large">
        <el-radio-button label="job-to-worker">为招聘信息匹配零工</el-radio-button>
        <el-radio-button label="worker-to-job">为零工匹配招聘信息</el-radio-button>
      </el-radio-group>
    </el-card>

    <el-row :gutter="20">
      <!-- 左侧：选择区域 -->
      <el-col :span="8">
        <el-card class="selection-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span>{{ matchType === 'job-to-worker' ? '选择招聘信息' : '选择零工' }}</span>
              <el-button type="primary" size="small" @click="handleRefreshList">
                <el-icon><Refresh /></el-icon>
                刷新
              </el-button>
            </div>
          </template>
          
          <!-- 搜索框 -->
          <el-input
            v-model="searchKeyword"
            placeholder="搜索..."
            clearable
            @input="handleSearch"
            class="search-input"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>

          <!-- 招聘信息列表 -->
          <div v-if="matchType === 'job-to-worker'" class="list-container">
            <div
              v-for="job in filteredJobList"
              :key="job.jobId"
              :class="['list-item', { 'active': selectedItem?.jobId === job.jobId }]"
              @click="handleSelectJob(job)"
            >
              <div class="item-header">
                <h4>{{ job.jobTitle }}</h4>
                <el-tag :type="getJobTypeTagType(job.jobType)" size="small">{{ job.jobType }}</el-tag>
              </div>
              <div class="item-content">
                <p><el-icon><Location /></el-icon> {{ job.workLocation }}</p>
                <p><el-icon><Money /></el-icon> {{ formatSalary(job) }}</p>
                <p><el-icon><User /></el-icon> {{ job.positionsFilled || 0 }}/{{ job.positionsAvailable || 0 }}</p>
              </div>
              <div class="item-footer">
                <el-tag :type="getUrgencyTagType(job.urgencyLevel)" size="small">
                  {{ getUrgencyText(job.urgencyLevel) }}
                </el-tag>
                <span class="create-time">{{ parseTime(job.createTime, '{m}-{d}') }}</span>
              </div>
            </div>
          </div>

          <!-- 零工列表 -->
          <div v-else class="list-container">
            <div
              v-for="worker in filteredWorkerList"
              :key="worker.workerId"
              :class="['list-item', { 'active': selectedItem?.workerId === worker.workerId }]"
              @click="handleSelectWorker(worker)"
            >
              <div class="item-header">
                <div class="worker-info">
                  <el-avatar :size="40" :src="worker.profilePhoto" :alt="worker.realName">
                    <el-icon><User /></el-icon>
                  </el-avatar>
                  <div class="worker-details">
                    <h4>{{ worker.realName }}</h4>
                    <span class="nickname">{{ worker.nickname }}</span>
                  </div>
                </div>
                <el-tag :type="worker.gender === 'male' ? 'primary' : 'danger'" size="small">
                  {{ worker.gender === 'male' ? '男' : '女' }}
                </el-tag>
              </div>
              <div class="item-content">
                <p><el-icon><Location /></el-icon> {{ worker.currentLocation }}</p>
                <p><el-icon><School /></el-icon> {{ worker.educationLevel }}</p>
                <p><el-icon><Trophy /></el-icon> {{ worker.workExperienceYears || 0 }}年经验</p>
              </div>
              <div class="item-footer">
                <el-rate 
                  v-model="worker.ratingAverage" 
                  disabled 
                  size="small"
                  :max="5"
                />
                <span class="rating-text">{{ (worker.ratingAverage || 0).toFixed(1) }}</span>
              </div>
            </div>
          </div>
        </el-card>
      </el-col>

      <!-- 右侧：匹配结果 -->
      <el-col :span="16">
        <el-card class="result-card" shadow="never">
          <template #header>
            <div class="card-header">
              <span>匹配结果</span>
              <div class="header-actions">
                <el-button 
                  type="primary" 
                  :disabled="!selectedItem" 
                  @click="handleStartMatch"
                  :loading="matchLoading"
                >
                  <el-icon><MagicStick /></el-icon>
                  开始匹配
                </el-button>
              </div>
            </div>
          </template>

          <!-- 匹配参数设置 -->
          <div v-if="selectedItem" class="match-params">
            <el-form :model="matchParams" :inline="true" size="small">
              <el-form-item label="匹配数量">
                <el-input-number v-model="matchParams.limit" :min="1" :max="50" />
              </el-form-item>
              <el-form-item label="最低匹配分数">
                <el-input-number v-model="matchParams.minScore" :min="0" :max="100" />
              </el-form-item>
              <el-form-item label="地理位置权重">
                <el-slider v-model="matchParams.locationWeight" :min="0" :max="100" style="width: 120px" />
              </el-form-item>
              <el-form-item label="薪资权重">
                <el-slider v-model="matchParams.salaryWeight" :min="0" :max="100" style="width: 120px" />
              </el-form-item>
              <el-form-item label="技能权重">
                <el-slider v-model="matchParams.skillWeight" :min="0" :max="100" style="width: 120px" />
              </el-form-item>
            </el-form>
          </div>

          <!-- 选中项信息 -->
          <div v-if="selectedItem" class="selected-info">
            <h3>{{ matchType === 'job-to-worker' ? '选中的招聘信息' : '选中的零工' }}</h3>
            
            <!-- 招聘信息详情 -->
            <div v-if="matchType === 'job-to-worker'" class="job-detail">
              <el-descriptions :column="2" border>
                <el-descriptions-item label="职位名称">{{ selectedItem.jobTitle }}</el-descriptions-item>
                <el-descriptions-item label="工作类型">{{ selectedItem.jobType }}</el-descriptions-item>
                <el-descriptions-item label="工作类别">{{ selectedItem.jobCategory }}</el-descriptions-item>
                <el-descriptions-item label="工作地点">{{ selectedItem.workLocation }}</el-descriptions-item>
                <el-descriptions-item label="薪资范围">{{ formatSalary(selectedItem) }}</el-descriptions-item>
                <el-descriptions-item label="招聘人数">{{ selectedItem.positionsAvailable }}</el-descriptions-item>
                <el-descriptions-item label="紧急程度">{{ getUrgencyText(selectedItem.urgencyLevel) }}</el-descriptions-item>
                <el-descriptions-item label="发布时间">{{ parseTime(selectedItem.createTime) }}</el-descriptions-item>
              </el-descriptions>
            </div>

            <!-- 零工详情 -->
            <div v-else class="worker-detail">
              <el-descriptions :column="2" border>
                <el-descriptions-item label="真实姓名">{{ selectedItem.realName }}</el-descriptions-item>
                <el-descriptions-item label="性别">{{ selectedItem.gender === 'male' ? '男' : '女' }}</el-descriptions-item>
                <el-descriptions-item label="年龄">{{ selectedItem.age }}岁</el-descriptions-item>
                <el-descriptions-item label="所在地">{{ selectedItem.currentLocation }}</el-descriptions-item>
                <el-descriptions-item label="学历">{{ selectedItem.educationLevel }}</el-descriptions-item>
                <el-descriptions-item label="工作经验">{{ selectedItem.workExperienceYears || 0 }}年</el-descriptions-item>
                <el-descriptions-item label="平均评分">{{ (selectedItem.ratingAverage || 0).toFixed(1) }}分</el-descriptions-item>
                <el-descriptions-item label="完成工作">{{ selectedItem.completedJobs || 0 }}个</el-descriptions-item>
              </el-descriptions>
            </div>
          </div>

          <!-- 匹配结果列表 -->
          <div v-if="matchResults.length > 0" class="match-results">
            <h3>匹配结果 ({{ matchResults.length }}个)</h3>
            
            <div class="result-list">
              <div
                v-for="(result, index) in matchResults"
                :key="matchType === 'job-to-worker' ? result.workerId : result.jobId"
                class="result-item"
              >
                <div class="result-header">
                  <div class="result-rank">{{ index + 1 }}</div>
                  <div class="result-score">
                    <el-progress 
                      :percentage="result.matchScore" 
                      :color="getScoreColor(result.matchScore)"
                      :stroke-width="8"
                    />
                    <span class="score-text">{{ result.matchScore }}%</span>
                  </div>
                </div>
                
                <!-- 零工匹配结果 -->
                <div v-if="matchType === 'job-to-worker'" class="worker-result">
                  <div class="worker-basic">
                    <el-avatar :size="50" :src="result.profilePhoto" :alt="result.realName">
                      <el-icon><User /></el-icon>
                    </el-avatar>
                    <div class="worker-info">
                      <h4>{{ result.realName }}</h4>
                      <p>{{ result.nickname }} | {{ result.gender === 'male' ? '男' : '女' }} | {{ result.age }}岁</p>
                      <p><el-icon><Location /></el-icon> {{ result.currentLocation }}</p>
                    </div>
                  </div>
                  <div class="worker-stats">
                    <el-tag type="success">{{ result.educationLevel }}</el-tag>
                    <el-tag type="info">{{ result.workExperienceYears || 0 }}年经验</el-tag>
                    <el-rate v-model="result.ratingAverage" disabled size="small" />
                  </div>
                </div>

                <!-- 招聘匹配结果 -->
                <div v-else class="job-result">
                  <div class="job-basic">
                    <div class="job-info">
                      <h4>{{ result.jobTitle }}</h4>
                      <p>{{ result.companyName }}</p>
                      <p><el-icon><Location /></el-icon> {{ result.workLocation }}</p>
                    </div>
                  </div>
                  <div class="job-stats">
                    <el-tag :type="getJobTypeTagType(result.jobType)">{{ result.jobType }}</el-tag>
                    <el-tag type="info">{{ result.jobCategory }}</el-tag>
                    <span class="salary">{{ formatSalary(result) }}</span>
                  </div>
                </div>

                <div class="result-actions">
                  <el-button size="small" @click="handleViewDetail(result)">查看详情</el-button>
                  <el-button type="primary" size="small" @click="handleContact(result)">联系</el-button>
                </div>
              </div>
            </div>
          </div>

          <!-- 空状态 -->
          <el-empty v-else-if="!selectedItem" description="请先选择要匹配的项目" />
          <el-empty v-else-if="!matchLoading" description="点击开始匹配按钮进行智能匹配" />
        </el-card>
      </el-col>
    </el-row>
  </div>
</template>

<script setup name="JobMatch">
import { ref, reactive, computed, onMounted, getCurrentInstance } from 'vue'
import {
  Refresh,
  Search,
  Location,
  Money,
  User,
  School,
  Trophy,
  MagicStick
} from '@element-plus/icons-vue'
import {
  listPublishedJobPosting,
  getJobPosting,
  matchWorkerProfileForJob
} from "@/api/job/posting"
import {
  listActiveWorkerProfile,
  getWorkerProfile,
  matchJobPostingForWorker
} from "@/api/job/worker"
import { parseTime } from "@/utils/ruoyi"

const { proxy } = getCurrentInstance()

// 响应式数据
const matchType = ref('job-to-worker') // job-to-worker 或 worker-to-job
const searchKeyword = ref('')
const selectedItem = ref(null)
const matchLoading = ref(false)
const jobList = ref([])
const workerList = ref([])
const matchResults = ref([])

// 匹配参数
const matchParams = reactive({
  limit: 10,
  minScore: 60,
  locationWeight: 30,
  salaryWeight: 25,
  skillWeight: 25,
  experienceWeight: 20
})

// 计算属性
const filteredJobList = computed(() => {
  if (!searchKeyword.value) return jobList.value
  return jobList.value.filter(job =>
    job.jobTitle.includes(searchKeyword.value) ||
    job.workLocation.includes(searchKeyword.value) ||
    job.jobCategory.includes(searchKeyword.value)
  )
})

const filteredWorkerList = computed(() => {
  if (!searchKeyword.value) return workerList.value
  return workerList.value.filter(worker =>
    worker.realName.includes(searchKeyword.value) ||
    worker.nickname?.includes(searchKeyword.value) ||
    worker.currentLocation?.includes(searchKeyword.value)
  )
})

// 生命周期
onMounted(() => {
  loadJobList()
  loadWorkerList()
})

// 方法
const loadJobList = async () => {
  try {
    const response = await listPublishedJobPosting({ pageNum: 1, pageSize: 100 })
    jobList.value = response.rows || []
  } catch (error) {
    console.error('加载招聘信息失败:', error)
  }
}

const loadWorkerList = async () => {
  try {
    const response = await listActiveWorkerProfile({ pageNum: 1, pageSize: 100 })
    workerList.value = response.rows || []
  } catch (error) {
    console.error('加载零工信息失败:', error)
  }
}

const handleMatchTypeChange = () => {
  selectedItem.value = null
  matchResults.value = []
  searchKeyword.value = ''
}

const handleRefreshList = () => {
  if (matchType.value === 'job-to-worker') {
    loadJobList()
  } else {
    loadWorkerList()
  }
}

const handleSearch = () => {
  // 搜索逻辑已在计算属性中实现
}

const handleSelectJob = (job) => {
  selectedItem.value = job
  matchResults.value = []
}

const handleSelectWorker = (worker) => {
  selectedItem.value = worker
  matchResults.value = []
}

const handleStartMatch = async () => {
  if (!selectedItem.value) return

  matchLoading.value = true
  try {
    let response
    if (matchType.value === 'job-to-worker') {
      // 为招聘信息匹配零工
      response = await matchWorkerProfileForJob(selectedItem.value.jobId, matchParams.limit)
    } else {
      // 为零工匹配招聘信息
      response = await matchJobPostingForWorker(selectedItem.value.workerId, matchParams.limit)
    }

    // 模拟匹配分数计算（实际应该由后端计算）
    const results = (response.data || []).map(item => ({
      ...item,
      matchScore: calculateMatchScore(item)
    }))

    // 按匹配分数排序
    matchResults.value = results
      .filter(item => item.matchScore >= matchParams.minScore)
      .sort((a, b) => b.matchScore - a.matchScore)

    proxy.$modal.msgSuccess(`匹配完成，找到 ${matchResults.value.length} 个匹配结果`)
  } catch (error) {
    console.error('匹配失败:', error)
    proxy.$modal.msgError('匹配失败，请重试')
  } finally {
    matchLoading.value = false
  }
}

// 计算匹配分数（简化版本，实际应该由后端实现）
const calculateMatchScore = (item) => {
  let score = 0
  let totalWeight = 0

  if (matchType.value === 'job-to-worker') {
    // 为招聘信息匹配零工的评分逻辑
    const job = selectedItem.value
    const worker = item

    // 地理位置匹配
    if (job.workLocation && worker.currentLocation) {
      const locationMatch = job.workLocation.includes(worker.currentLocation) ||
                           worker.currentLocation.includes(job.workLocation)
      score += locationMatch ? matchParams.locationWeight : matchParams.locationWeight * 0.3
    }
    totalWeight += matchParams.locationWeight

    // 薪资匹配
    if (job.salaryMin && worker.salaryExpectationMin) {
      const salaryMatch = job.salaryMin >= worker.salaryExpectationMin * 0.8
      score += salaryMatch ? matchParams.salaryWeight : matchParams.salaryWeight * 0.5
    }
    totalWeight += matchParams.salaryWeight

    // 技能匹配（简化）
    score += matchParams.skillWeight * 0.7 // 假设70%匹配
    totalWeight += matchParams.skillWeight

    // 经验匹配
    if (worker.workExperienceYears >= 0) {
      const experienceScore = Math.min(worker.workExperienceYears / 5, 1) // 5年经验为满分
      score += matchParams.experienceWeight * experienceScore
    }
    totalWeight += matchParams.experienceWeight

  } else {
    // 为零工匹配招聘信息的评分逻辑
    const worker = selectedItem.value
    const job = item

    // 类似的匹配逻辑...
    score = 75 + Math.random() * 20 // 简化为随机分数
    totalWeight = 100
  }

  return Math.round((score / totalWeight) * 100)
}

const handleViewDetail = (item) => {
  if (matchType.value === 'job-to-worker') {
    // 查看零工详情
    proxy.$router.push(`/job/worker/detail/${item.workerId}`)
  } else {
    // 查看招聘详情
    proxy.$router.push(`/job/posting/detail/${item.jobId}`)
  }
}

const handleContact = (item) => {
  if (matchType.value === 'job-to-worker') {
    // 联系零工
    proxy.$modal.msgInfo(`联系零工：${item.realName}，电话：${item.phone}`)
  } else {
    // 联系雇主
    proxy.$modal.msgInfo(`联系雇主：${item.contactPerson}，电话：${item.contactPhone}`)
  }
}

// 工具方法
const formatSalary = (item) => {
  if (item.salaryMin && item.salaryMax) {
    return `${item.salaryMin}-${item.salaryMax} ${getSalaryTypeText(item.salaryType)}`
  } else if (item.salaryMin) {
    return `${item.salaryMin}+ ${getSalaryTypeText(item.salaryType)}`
  }
  return '面议'
}

const getSalaryTypeText = (salaryType) => {
  const textMap = {
    'hourly': '元/小时',
    'daily': '元/天',
    'monthly': '元/月',
    'piece': '元/件'
  }
  return textMap[salaryType] || '元'
}

const getJobTypeTagType = (jobType) => {
  const typeMap = {
    '全职': 'success',
    '兼职': 'info',
    '临时工': 'warning',
    '小时工': 'danger',
    '实习': ''
  }
  return typeMap[jobType] || ''
}

const getUrgencyTagType = (urgency) => {
  const typeMap = {
    'urgent': 'danger',
    'high': 'warning',
    'normal': 'info',
    'low': ''
  }
  return typeMap[urgency] || 'info'
}

const getUrgencyText = (urgency) => {
  const textMap = {
    'urgent': '紧急',
    'high': '高',
    'normal': '普通',
    'low': '低'
  }
  return textMap[urgency] || '普通'
}

const getScoreColor = (score) => {
  if (score >= 90) return '#67c23a'
  if (score >= 80) return '#e6a23c'
  if (score >= 70) return '#f56c6c'
  return '#909399'
}
</script>

<style scoped>
.app-container {
  padding: 20px;
}

.match-type-card {
  margin-bottom: 20px;
  text-align: center;
}

.match-type-card .el-radio-group {
  display: inline-flex;
}

.selection-card, .result-card {
  height: calc(100vh - 200px);
  overflow: hidden;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.search-input {
  margin-bottom: 15px;
}

.list-container {
  height: calc(100% - 60px);
  overflow-y: auto;
}

.list-item {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 10px;
  cursor: pointer;
  transition: all 0.3s;
}

.list-item:hover {
  border-color: #409eff;
  box-shadow: 0 2px 8px rgba(64, 158, 255, 0.1);
}

.list-item.active {
  border-color: #409eff;
  background-color: #f0f8ff;
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 10px;
}

.item-header h4 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #303133;
}

.worker-info {
  display: flex;
  align-items: center;
  gap: 10px;
}

.worker-details h4 {
  margin: 0;
  font-size: 14px;
}

.nickname {
  font-size: 12px;
  color: #909399;
}

.item-content {
  margin-bottom: 10px;
}

.item-content p {
  margin: 5px 0;
  font-size: 13px;
  color: #606266;
  display: flex;
  align-items: center;
  gap: 5px;
}

.item-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.create-time {
  font-size: 12px;
  color: #909399;
}

.rating-text {
  font-size: 12px;
  color: #606266;
  margin-left: 5px;
}

.match-params {
  background-color: #f8f9fa;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 20px;
}

.selected-info {
  margin-bottom: 20px;
}

.selected-info h3 {
  margin-bottom: 15px;
  color: #303133;
}

.match-results h3 {
  margin-bottom: 15px;
  color: #303133;
}

.result-list {
  max-height: 400px;
  overflow-y: auto;
}

.result-item {
  border: 1px solid #e4e7ed;
  border-radius: 8px;
  padding: 15px;
  margin-bottom: 15px;
  background-color: #fff;
}

.result-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.result-rank {
  width: 30px;
  height: 30px;
  border-radius: 50%;
  background-color: #409eff;
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
}

.result-score {
  display: flex;
  align-items: center;
  gap: 10px;
  flex: 1;
  margin-left: 20px;
}

.score-text {
  font-weight: bold;
  color: #303133;
}

.worker-result, .job-result {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15px;
}

.worker-basic, .job-basic {
  display: flex;
  align-items: center;
  gap: 15px;
  flex: 1;
}

.worker-info h4, .job-info h4 {
  margin: 0 0 5px 0;
  font-size: 16px;
  font-weight: 600;
}

.worker-info p, .job-info p {
  margin: 2px 0;
  font-size: 13px;
  color: #606266;
  display: flex;
  align-items: center;
  gap: 5px;
}

.worker-stats, .job-stats {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 8px;
}

.salary {
  font-weight: 600;
  color: #e6a23c;
}

.result-actions {
  display: flex;
  gap: 10px;
  justify-content: flex-end;
}

.el-card {
  border-radius: 8px;
}

.el-tag {
  border-radius: 4px;
}

.el-progress {
  flex: 1;
}

.el-descriptions {
  margin-top: 10px;
}

/* 滚动条样式 */
.list-container::-webkit-scrollbar,
.result-list::-webkit-scrollbar {
  width: 6px;
}

.list-container::-webkit-scrollbar-track,
.result-list::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.list-container::-webkit-scrollbar-thumb,
.result-list::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.list-container::-webkit-scrollbar-thumb:hover,
.result-list::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
