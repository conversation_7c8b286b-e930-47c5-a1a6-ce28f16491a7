<template>
  <el-dialog v-model="dialogVisible" :title="dialogTitle" :width="formOption.dialogWidth || '800px'" 
    :close-on-click-modal="false" :close-on-press-escape="false" append-to-body>
    
    <div class="form-container" :style="{ maxHeight: formOption.dialogHeight || '60vh', overflowY: 'auto' }">
      <el-form ref="formRef" :model="formData" :rules="formRules" :label-width="formOption.labelWidth || '100px'">
        <el-row :gutter="20">
          <template v-for="field in formFields" :key="field.prop">
            <!-- 分隔线 -->
            <el-col v-if="field.divider" :span="24" class="divider-col">
              <el-divider content-position="left">{{ field.label }}</el-divider>
            </el-col>
            
            <!-- 普通表单项 -->
            <el-col v-else :span="field.span || 12" class="form-col">
              <el-form-item :label="field.label" :prop="field.prop" :rules="field.rules">
                <!-- 输入框 -->
                <el-input 
                  v-if="!field.type || field.type === 'input'"
                  v-model="formData[field.prop]"
                  :placeholder="field.placeholder || `请输入${field.label}`"
                  :disabled="isViewMode || field.disabled"
                  :maxlength="field.maxlength"
                  :show-word-limit="field.showWordLimit"
                  clearable
                />
                
                <!-- 文本域 -->
                <el-input 
                  v-else-if="field.type === 'textarea'"
                  v-model="formData[field.prop]"
                  type="textarea"
                  :placeholder="field.placeholder || `请输入${field.label}`"
                  :disabled="isViewMode || field.disabled"
                  :rows="field.minRows || 4"
                  :maxlength="field.maxlength"
                  :show-word-limit="field.showWordLimit"
                  resize="vertical"
                />
                
                <!-- 选择器 -->
                <el-select 
                  v-else-if="field.type === 'select'"
                  v-model="formData[field.prop]"
                  :placeholder="field.placeholder || `请选择${field.label}`"
                  :disabled="isViewMode || field.disabled"
                  clearable
                  style="width: 100%"
                >
                  <el-option
                    v-for="option in field.dicData"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>
                
                <!-- 开关 -->
                <el-switch 
                  v-else-if="field.type === 'switch'"
                  v-model="formData[field.prop]"
                  :disabled="isViewMode || field.disabled"
                  active-value="0"
                  inactive-value="1"
                />
                
                <!-- 日期选择器 -->
                <el-date-picker
                  v-else-if="field.type === 'date'"
                  v-model="formData[field.prop]"
                  type="date"
                  :placeholder="field.placeholder || `请选择${field.label}`"
                  :disabled="isViewMode || field.disabled"
                  style="width: 100%"
                />
                
                <!-- 日期时间选择器 -->
                <el-date-picker
                  v-else-if="field.type === 'datetime'"
                  v-model="formData[field.prop]"
                  type="datetime"
                  :placeholder="field.placeholder || `请选择${field.label}`"
                  :disabled="isViewMode || field.disabled"
                  style="width: 100%"
                />
              </el-form-item>
            </el-col>
          </template>
        </el-row>
      </el-form>
    </div>
    
    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">取 消</el-button>
        <el-button v-if="!isViewMode" type="primary" :loading="submitLoading" @click="handleSubmit">
          确 定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, computed, nextTick } from 'vue'

// Props
const props = defineProps({
  formFields: {
    type: Array,
    default: () => []
  },
  formOption: {
    type: Object,
    default: () => ({})
  }
})

// Emits
const emit = defineEmits(['submit', 'cancel'])

// 响应式数据
const dialogVisible = ref(false)
const dialogTitle = ref('')
const dialogType = ref('add') // add, edit, view
const submitLoading = ref(false)
const formRef = ref(null)
const formData = reactive({})
const originalData = ref({})

// 计算属性
const isViewMode = computed(() => dialogType.value === 'view')

// 表单验证规则
const formRules = computed(() => {
  const rules = {}
  props.formFields.forEach(field => {
    if (field.rules && field.prop) {
      rules[field.prop] = field.rules
    }
  })
  return rules
})

// 打开弹窗
const openDialog = (type, title, data = {}) => {
  dialogType.value = type
  dialogTitle.value = title
  
  // 重置表单数据
  Object.keys(formData).forEach(key => {
    delete formData[key]
  })
  
  // 设置表单数据
  Object.assign(formData, data)
  originalData.value = { ...data }
  
  dialogVisible.value = true
  
  // 重置表单验证
  nextTick(() => {
    if (formRef.value) {
      formRef.value.clearValidate()
    }
  })
}

// 处理提交
const handleSubmit = () => {
  if (!formRef.value) return
  
  formRef.value.validate((valid) => {
    if (valid) {
      submitLoading.value = true
      
      // 发送提交事件
      emit('submit', {
        type: dialogType.value,
        data: { ...formData }
      })
    }
  })
}

// 处理取消
const handleCancel = () => {
  dialogVisible.value = false
  emit('cancel')
}

// 提交成功回调
const onSubmitSuccess = () => {
  submitLoading.value = false
  dialogVisible.value = false
}

// 提交失败回调
const onSubmitError = () => {
  submitLoading.value = false
}

// 暴露方法
defineExpose({
  openDialog,
  onSubmitSuccess,
  onSubmitError
})
</script>

<style lang="scss" scoped>
.form-container {
  padding: 0 10px;
}

.divider-col {
  margin: 10px 0;
  
  .el-divider {
    margin: 15px 0;
    
    :deep(.el-divider__text) {
      font-weight: 600;
      color: #409eff;
    }
  }
}

.form-col {
  margin-bottom: 10px;
}

.dialog-footer {
  text-align: right;
  
  .el-button {
    margin-left: 10px;
  }
}

:deep(.el-form-item) {
  margin-bottom: 18px;
}

:deep(.el-form-item__label) {
  font-weight: 500;
}
</style>
