# 政策申请两层审核系统

## 功能概述

本系统实现了政策申请的两层审核功能，包括初审和终审两个层级，支持不同权限用户查看不同的申请列表。

## 权限说明

### 权限配置

系统根据以下权限来控制用户可以执行的操作：

1. **初审权限**: `policy:application:first-review`
   - 可以查看待初审的申请列表
   - 可以对状态为"待初审"(0)的申请进行初审操作

2. **终审权限**: `policy:application:final-review`
   - 可以查看待终审的申请列表
   - 可以对状态为"待终审"(3)的申请进行终审操作

3. **管理员权限**: `policy:application:admin` 或 `admin` 角色
   - 可以查看所有申请列表
   - 可以编辑和删除申请
   - 拥有完整的管理权限

### 权限判断逻辑

```javascript
// 根据权限选择不同的API
let listFunction = listAllApplications; // 默认查询所有

if (hasFirstReviewPermission.value && !hasAdminPermission.value) {
  // 只有初审权限，查询待初审的申请
  listFunction = listPendingFirstReview;
} else if (hasFinalReviewPermission.value && !hasAdminPermission.value) {
  // 只有终审权限，查询待终审的申请
  listFunction = listPendingFinalReview;
}
```

## 申请状态流转

申请状态按以下流程进行流转：

```
待初审(0) → 初审通过(1) → 待终审(3) → 终审通过(4) → 已完成(6)
     ↓              ↓
   初审拒绝(2)    终审拒绝(5)
```

### 状态说明

- `0`: 待初审 - 申请刚提交，等待初审员审核
- `1`: 初审通过 - 初审员审核通过，自动流转到待终审状态
- `2`: 初审拒绝 - 初审员审核拒绝，流程结束
- `3`: 待终审 - 等待终审员审核
- `4`: 终审通过 - 终审员审核通过，自动流转到已完成状态
- `5`: 终审拒绝 - 终审员审核拒绝，流程结束
- `6`: 已完成 - 审核流程完成

## API接口说明

### 查询接口

1. **查询所有申请**: `GET /policy/application/all`
   - 管理员权限使用
   - 返回所有申请列表

2. **查询待初审申请**: `GET /policy/application/pending-first-review`
   - 初审员权限使用
   - 返回状态为"待初审"的申请列表

3. **查询待终审申请**: `GET /policy/application/pending-final-review`
   - 终审员权限使用
   - 返回状态为"待终审"的申请列表

### 审核接口

1. **初审操作**: `POST /policy/application/first-review`
   ```json
   {
     "applicationId": 1,
     "approvalStatus": "1", // 1-通过, 2-拒绝
     "approvalComment": "审核意见"
   }
   ```

2. **终审操作**: `POST /policy/application/final-review`
   ```json
   {
     "applicationId": 1,
     "approvalStatus": "1", // 1-通过, 2-拒绝
     "approvalComment": "审核意见"
   }
   ```

3. **查询审核记录**: `GET /policy/application/approval-records/{applicationId}`
   - 返回指定申请的所有审核记录

## 组件说明

### 主要组件

1. **index.vue**: 主页面组件
   - 展示申请列表
   - 根据权限显示不同的操作按钮
   - 处理审核操作

2. **ReviewDialog.vue**: 审核弹窗组件
   - 显示申请基本信息
   - 提供审核表单（审核结果、审核意见、相关文件）
   - 支持初审和终审两种模式

3. **ApprovalRecordsDialog.vue**: 审核记录弹窗组件
   - 以时间线形式展示审核历史
   - 显示每个审核节点的详细信息
   - 支持查看审核文件

4. **ApplicationFormDialog.vue**: 申请详情弹窗组件
   - 展示申请的详细信息
   - 支持查看、编辑模式
   - 动态表单渲染

### 配置文件

**application.js**: 政策申请的表格和表单配置
- 定义表格列配置
- 定义表单字段配置
- 定义审核表单配置

## 使用示例

### 1. 初审员操作流程

1. 登录系统，具有 `policy:application:first-review` 权限
2. 进入政策计划页面，自动显示待初审申请列表
3. 点击"查看"按钮查看申请详情
4. 点击"审核记录"按钮查看历史审核记录
5. 点击"初审"按钮进行审核操作
6. 填写审核结果和意见，提交审核

### 2. 终审员操作流程

1. 登录系统，具有 `policy:application:final-review` 权限
2. 进入政策计划页面，自动显示待终审申请列表
3. 查看申请详情和审核记录
4. 点击"终审"按钮进行最终审核
5. 填写审核结果和意见，完成审核流程

### 3. 管理员操作流程

1. 登录系统，具有管理员权限
2. 进入政策计划页面，显示所有申请列表
3. 可以查看、编辑、删除申请
4. 可以查看所有审核记录
5. 具有完整的管理权限

## 注意事项

1. **权限控制**: 系统严格按照权限控制用户可见的数据和操作
2. **状态流转**: 审核状态按照预定义流程自动流转，不可逆转
3. **审核记录**: 所有审核操作都会记录在审核记录表中，便于追溯
4. **文件上传**: 审核时可以上传相关文件作为审核依据
5. **表单验证**: 所有表单都有完整的验证规则，确保数据完整性

## 扩展说明

如需扩展更多审核层级或修改审核流程，可以：

1. 修改状态定义和流转逻辑
2. 添加新的权限配置
3. 扩展API接口
4. 调整组件显示逻辑

系统设计具有良好的扩展性，可以根据实际业务需求进行调整。
