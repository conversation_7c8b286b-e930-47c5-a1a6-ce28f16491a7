import request from '@/utils/request'

// 查询政策信息列表
export function listPolicyInfo(query) {
  return request({
    url: '/policy/info/list',
    method: 'get',
    params: query
  })
}

// 查询政策信息详细
export function getPolicyInfo(policyId) {
  return request({
    url: '/policy/info/' + policyId,
    method: 'get'
  })
}

// 新增政策信息
export function addPolicyInfo(data) {
  return request({
    url: '/policy/info',
    method: 'post',
    data: data
  })
}

// 修改政策信息
export function updatePolicyInfo(data) {
  return request({
    url: '/policy/info',
    method: 'put',
    data: data
  })
}

// 删除政策信息
export function delPolicyInfo(policyId) {
  return request({
    url: '/policy/info/' + policyId,
    method: 'delete'
  })
}
