# 用户ID后端获取更新

## 更新概述

根据您的要求，已将申请人用户ID的获取方式从前端store改为后端自动获取，提高了安全性和数据一致性。

## 更新内容

### 1. 前端更新

#### 文件位置
- `Policy-Vue3/src/views/index.vue`

#### 主要变更
**修改前：**
```javascript
// 准备申请数据
const applicationData = {
  policyId: applyForm.policyId,
  applicantUserId: proxy.$store.state.user.id, // 从前端store获取
  requiredMaterials: JSON.stringify(requiredMaterials.value),
  remark: applyForm.remark
}
```

**修改后：**
```javascript
// 准备申请数据
const applicationData = {
  policyId: applyForm.policyId,
  // applicantUserId 由后端根据当前登录用户自动设置
  requiredMaterials: JSON.stringify(requiredMaterials.value),
  remark: applyForm.remark
}
```

#### 变更说明
- 移除了从前端store获取用户ID的逻辑
- 前端不再传递 `applicantUserId` 字段
- 由后端根据当前登录用户自动设置申请人ID

### 2. 后端更新

#### 文件位置
- `Policy-Springboot3/sux-system/src/main/java/com/sux/system/service/impl/PolicyApplicationServiceImpl.java`

#### 主要变更
**修改前：**
```java
@Override
@Transactional
public int insertPolicyApplication(PolicyApplication policyApplication)
{
    policyApplication.setCreateId(SecurityUtils.getUserId());
    policyApplication.setCreateTime(DateUtils.getNowDate());
    policyApplication.setSubmitTime(DateUtils.getNowDate());
    policyApplication.setApplicationStatus("0"); // 默认待初审状态
    
    int result = policyApplicationMapper.insertPolicyApplication(policyApplication);
    
    // 创建初审和终审记录
    if (result > 0) {
        createApprovalRecords(policyApplication.getApplicationId());
    }
    
    return result;
}
```

**修改后：**
```java
@Override
@Transactional
public int insertPolicyApplication(PolicyApplication policyApplication)
{
    Long currentUserId = SecurityUtils.getUserId();
    
    policyApplication.setApplicantUserId(currentUserId); // 设置申请人为当前登录用户
    policyApplication.setCreateId(currentUserId);
    policyApplication.setCreateTime(DateUtils.getNowDate());
    policyApplication.setSubmitTime(DateUtils.getNowDate());
    policyApplication.setApplicationStatus("0"); // 默认待初审状态
    
    int result = policyApplicationMapper.insertPolicyApplication(policyApplication);
    
    // 创建初审和终审记录
    if (result > 0) {
        createApprovalRecords(policyApplication.getApplicationId());
    }
    
    return result;
}
```

#### 变更说明
- 使用 `SecurityUtils.getUserId()` 获取当前登录用户ID
- 自动设置 `applicantUserId` 为当前登录用户
- 确保申请人ID的准确性和安全性

### 3. 实体类更新

#### 文件位置
- `Policy-Springboot3/sux-system/src/main/java/com/sux/system/domain/PolicyApplication.java`

#### 主要变更
**修改前：**
```java
/** 申请人用户ID */
@Excel(name = "申请人用户ID")
@NotNull(message = "申请人用户ID不能为空")
private Long applicantUserId;
```

**修改后：**
```java
/** 申请人用户ID */
@Excel(name = "申请人用户ID")
private Long applicantUserId;
```

#### 变更说明
- 移除了 `@NotNull` 验证注解
- 因为现在由后端自动设置，不需要前端传递
- 避免了验证错误

## 技术优势

### 1. 安全性提升
- **防止伪造**: 前端无法伪造其他用户的ID
- **数据一致性**: 确保申请人ID与当前登录用户一致
- **权限控制**: 基于服务器端的用户认证信息

### 2. 代码简化
- **前端简化**: 不需要处理用户ID获取逻辑
- **逻辑集中**: 用户ID处理逻辑集中在后端
- **维护性**: 减少前后端数据同步问题

### 3. 可靠性增强
- **服务器权威**: 以服务器端的用户认证为准
- **避免错误**: 减少前端数据传递错误的可能性
- **统一处理**: 所有用户ID相关逻辑统一处理

## 工作流程

### 申请提交流程
1. **前端**: 用户填写申请信息和上传材料
2. **前端**: 提交申请数据（不包含用户ID）
3. **后端**: 接收申请请求
4. **后端**: 从Spring Security上下文获取当前用户ID
5. **后端**: 自动设置申请人ID为当前登录用户
6. **后端**: 保存申请数据到数据库
7. **后端**: 创建对应的审核记录
8. **前端**: 接收成功响应并提示用户

### 安全验证
- 使用Spring Security的 `SecurityUtils.getUserId()` 方法
- 基于JWT Token或Session的用户认证
- 确保只有登录用户才能提交申请
- 申请人ID与登录用户ID强制一致

## 兼容性说明

### 向后兼容
- 现有的申请数据不受影响
- API接口保持不变
- 前端调用方式基本不变（只是少传一个字段）

### 数据库兼容
- 数据库表结构无需修改
- 现有数据的申请人ID保持不变
- 新申请的申请人ID由后端自动设置

## 测试建议

### 功能测试
1. **正常申请**: 验证申请提交后申请人ID正确设置
2. **权限测试**: 验证未登录用户无法提交申请
3. **数据一致性**: 验证申请人ID与登录用户ID一致

### 安全测试
1. **伪造测试**: 尝试在请求中传递其他用户ID，验证被忽略
2. **认证测试**: 验证Token过期或无效时的处理
3. **权限测试**: 验证不同角色用户的申请权限

这个更新提高了系统的安全性和可靠性，确保申请人身份的真实性和准确性。
