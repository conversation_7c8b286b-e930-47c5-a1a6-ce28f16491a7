import { parseTime } from "@/utils/ruoyi";

/**
 * 岗位管理 - 表格配置
 */
export const createPostTableOption = (proxy) => {
    const {
        sys_normal_disable
    } = proxy.useDict("sys_normal_disable");

    return {
        dialogWidth: '600px',  // 弹窗宽度
        dialogHeight: '65vh',  // 弹窗内容区最大高度
        labelWidth: '100px',
        column: [
            // ==================== 基础信息分组 ====================
            {
                label: "基础信息",
                prop: "divider_basic_info",
                formSlot: true,
                headerAlign: "left",
                labelWidth: 0,
                span: 24,
                divider: true // 分隔线标识
            },
            {
                label: "岗位编号",
                prop: "postId",
                minWidth: 100,
                align: "center",
                addDisplay: false,
                editDisplay: false,
                viewDisplay: true,
                search: false
            },
            {
                label: "岗位名称",
                prop: "postName",
                search: true,
                rules: [{
                    required: true,
                    message: "岗位名称不能为空",
                    trigger: "blur"
                }, {
                    min: 2,
                    max: 50,
                    message: "岗位名称长度必须介于 2 和 50 之间",
                    trigger: "blur"
                }],
                span: 12,
                minWidth: 150,
                placeholder: "请输入岗位名称",
                showOverflowTooltip: true
            },
            {
                label: "岗位编码",
                prop: "postCode",
                search: true,
                rules: [{
                    required: true,
                    message: "岗位编码不能为空",
                    trigger: "blur"
                }, {
                    min: 2,
                    max: 64,
                    message: "岗位编码长度必须介于 2 和 64 之间",
                    trigger: "blur"
                }, {
                    pattern: /^[a-zA-Z0-9_]+$/,
                    message: "岗位编码只能包含字母、数字和下划线",
                    trigger: "blur"
                }],
                span: 12,
                minWidth: 150,
                placeholder: "请输入岗位编码",
                showOverflowTooltip: true
            },
            {
                label: "岗位顺序",
                prop: "postSort",
                type: 'number',
                rules: [{
                    required: true,
                    message: "岗位顺序不能为空",
                    trigger: "blur"
                }],
                span: 12,
                minWidth: 120,
                align: 'center',
                min: 0,
                max: 999,
                controlsPosition: "right",
                placeholder: "请输入岗位顺序",
                defaultValue: 0
            },
            {
                label: "岗位状态",
                prop: "status",
                type: 'radio',
                span: 12,
                minWidth: 100,
                search: true,
                dicData: sys_normal_disable,
                slot: true, // 表格中使用开关组件
                defaultValue: "0"
            },
            {
                label: "创建时间",
                prop: "createTime",
                editDisplay: false,
                addDisplay: false,
                type: 'datetime',
                minWidth: 180,
                align: "center",
                search: true,
                searchRange: true, // 支持范围搜索
                formatter: (row, column, cellValue) => {
                    return parseTime(cellValue, '{y}-{m}-{d} {h}:{i}:{s}');
                }
            },

            // ==================== 详细信息分组 ====================
            {
                label: "详细信息",
                prop: "divider_detail_info",
                formSlot: true,
                headerAlign: "left",
                labelWidth: 0,
                span: 24,
                divider: true // 分隔线标识
            },
            {
                label: "备注",
                prop: "remark",
                type: 'textarea',
                minRows: 3,
                maxRows: 6,
                span: 24,
                showColumn: false, // 在表格中不显示
                placeholder: "请输入备注信息",
                showWordLimit: true,
                maxlength: 500
            }
        ]
    };
};

 