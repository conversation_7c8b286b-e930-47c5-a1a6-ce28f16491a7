<template>
    <el-dialog v-model="dialogVisible" :title="dialogTitle" :width="formOption.dialogWidth" destroy-on-close
        :close-on-click-modal="false" :fullscreen="isFullscreen" @closed="handleDialogClosed" @open="handleDialogOpened"
        class="custom-dialog">
        <div class="dialog-content" :class="{ 'view-mode': dialogType === 'view' }" :style="dialogContentStyle">
            <!-- 编辑模式使用FormList组件 -->
            <FormList v-if="dialogType !== 'view'" ref="formListRef" v-model="formData" :fields="formFields"
                :is-view="false" :showActions="false" :labelWidth="formOption.labelWidth" :inline="false"
                @field-change="handleFieldChange">
                <!-- 上级菜单选择插槽 -->
                <template #parentId="{ row }">
                    <el-tree-select v-model="formData.parentId" :data="menuOptions"
                        :props="{ value: 'menuId', label: 'menuName', children: 'children' }" value-key="menuId"
                        placeholder="选择上级菜单" check-strictly style="width: 100%" />
                </template>

                <!-- 菜单图标选择插槽 -->
                <template #icon="{ row }">
                    <el-popover placement="bottom-start" :width="540" trigger="click">
                        <template #reference>
                            <el-input v-model="formData.icon" placeholder="点击选择图标" @blur="showSelectIcon" readonly
                                style="width: 100%">
                                <template #prefix>
                                    <svg-icon v-if="formData.icon" :icon-class="formData.icon" class="el-input__icon"
                                        style="height: 32px;width: 16px;" />
                                    <el-icon v-else style="height: 32px;width: 16px;">
                                        <Search />
                                    </el-icon>
                                </template>
                            </el-input>
                        </template>
                        <icon-select ref="iconSelectRef" @selected="selected" :active-icon="formData.icon" />
                    </el-popover>
                </template>


            </FormList>

            <!-- 查看模式使用ViewList组件 -->
            <ViewList v-else v-model="formData" :fields="formFields" :labelWidth="formOption.labelWidth">
                <!-- 上级菜单查看插槽 -->
                <template #parentId="{ row }">
                    <span>{{ getParentMenuName(formData.parentId) }}</span>
                </template>

                <!-- 菜单图标查看插槽 -->
                <template #icon="{ row }">
                    <svg-icon v-if="formData.icon" :icon-class="formData.icon" style="font-size: 16px;" />
                    <span v-else>-</span>
                </template>

                <!-- 菜单类型查看插槽 -->
                <template #menuType="{ row }">
                    <el-tag :type="getMenuTypeTagType(formData.menuType)" size="small">
                        {{ getMenuTypeLabel(formData.menuType) }}
                    </el-tag>
                </template>

                <!-- 显示状态查看插槽 -->
                <template #visible="{ row }">
                    <el-tag :type="formData.visible === '0' ? 'success' : 'info'">
                        {{ formData.visible === '0' ? '显示' : '隐藏' }}
                    </el-tag>
                </template>

                <!-- 菜单状态查看插槽 -->
                <template #status="{ row }">
                    <el-tag :type="formData.status === '0' ? 'success' : 'danger'">
                        {{ formData.status === '0' ? '正常' : '停用' }}
                    </el-tag>
                </template>

                <!-- 是否外链查看插槽 -->
                <template #isFrame="{ row }">
                    <el-tag :type="formData.isFrame === '0' ? 'warning' : 'info'">
                        {{ formData.isFrame === '0' ? '是' : '否' }}
                    </el-tag>
                </template>

                <!-- 是否缓存查看插槽 -->
                <template #isCache="{ row }">
                    <el-tag :type="formData.isCache === '0' ? 'success' : 'info'">
                        {{ formData.isCache === '0' ? '缓存' : '不缓存' }}
                    </el-tag>
                </template>
            </ViewList>
        </div>

        <!-- 操作按钮 -->
        <template #footer>
            <span class="dialog-footer">
                <el-button class="custom-btn" @click="toggleFullscreen">
                    {{ isFullscreen ? '退出全屏' : '全屏显示' }}
                </el-button>
                <el-button class="custom-btn" @click="handleCancel">
                    {{ dialogType === 'view' ? '关闭' : '取消' }}
                </el-button>
                <el-button v-if="dialogType !== 'view'" type="primary" class="custom-btn"
                    @click="handleSubmitForm" :loading="submitLoading" :disabled="submitDisabled">
                    确 认
                </el-button>
            </span>
        </template>
    </el-dialog>
</template>

<script setup name="MenuFormDialog">
import { ref, reactive, computed, getCurrentInstance } from 'vue'
import { Search } from '@element-plus/icons-vue'
import FormList from '@/components/FormList/index.vue'
import ViewList from '@/components/ViewList/index.vue'
import SvgIcon from '@/components/SvgIcon'
import IconSelect from '@/components/IconSelect'
import { createMenuFormOption } from '@/const/system/menu'
import { getCoSyncColumn, extractTableColumns } from "@/utils/columnUtils"
import { getMenu, addMenu, updateMenu, listMenu } from '@/api/system/menu'
// Props
const props = defineProps({
    parentMenuId: {
        type: [String, Number],
        default: 0
    }
})

// Emits
const emit = defineEmits(['submit', 'cancel'])

// 获取代理对象
const { proxy } = getCurrentInstance()

// 响应式数据
const formListRef = ref()
const iconSelectRef = ref()
const dialogVisible = ref(false)
const dialogType = ref('add')
const dialogTitle = ref('新增菜单')
const formData = ref({})
const submitLoading = ref(false)
const submitDisabled = ref(false)
const menuOptions = ref([])
const isFullscreen = ref(false)

// 配置数据
const formOption = ref({
    dialogWidth: '800px',
    dialogHeight: '70vh',
    labelWidth: '120px'
})
const formFields = ref([])

// 初始化配置
const initializeConfig = async () => {
    try {
        // 获取基础配置
        const baseOption = createMenuFormOption(proxy)

        // 使用工具类获取合并后的配置
        const mergedConfig = await getCoSyncColumn({
            baseOption,
            proxy
        })

        // 使用工具类提取完整配置 - 包含表单字段和表单选项
        const { formFields: extractedFormFields, formOptions } = extractTableColumns(mergedConfig)

        // 设置表单字段配置
        formFields.value = extractedFormFields

        // 设置表单选项配置
        formOption.value = {
            ...formOption.value, // 保留默认配置
            ...formOptions       // 使用从配置文件中提取的完整选项
        }
    } catch (error) {
        console.error('初始化配置失败:', error)
    }
}

// 弹窗内容样式计算
const dialogContentStyle = computed(() => {
    const baseStyle = {
        overflow: 'visible',
        padding: '20px 10px',
        overflowX: 'hidden',
    }

    if (isFullscreen.value) {
        return {
            ...baseStyle,
            maxHeight: 'calc(100vh - 180px)',
            overflowY: 'auto',
            overflowX: 'hidden',
        }
    }

    return {
        ...baseStyle,
        maxHeight: formOption.dialogHeight || '70vh', // 只设置最大高度
        overflowY: 'auto',
        overflowX: 'hidden',
        minHeight: 'auto', // 允许自适应最小高度
    }
})

/**
 * 查询菜单下拉树结构
 */
const getTreeselect = async () => {
    try {
        menuOptions.value = []
        const response = await listMenu()
        const menu = { menuId: 0, menuName: "主类目", children: [] }
        menu.children = proxy.handleTree(response.data, "menuId")
        menuOptions.value.push(menu)
    } catch (error) {
        console.error('获取菜单树失败:', error)
    }
}

/**
 * 获取上级菜单名称
 */
const getParentMenuName = (parentId) => {
    if (parentId === 0) return '主类目'

    const findMenuName = (menus, id) => {
        for (const menu of menus) {
            if (menu.menuId === id) return menu.menuName
            if (menu.children) {
                const found = findMenuName(menu.children, id)
                if (found) return found
            }
        }
        return null
    }

    return findMenuName(menuOptions.value, parentId) || '-'
}

/**
 * 获取菜单类型标签类型
 */
const getMenuTypeTagType = (type) => {
    const typeMap = {
        'M': 'info',
        'C': 'success',
        'F': 'warning'
    }
    return typeMap[type] || 'info'
}

/**
 * 获取菜单类型标签文本
 */
const getMenuTypeLabel = (type) => {
    const typeMap = {
        'M': '目录',
        'C': '菜单',
        'F': '按钮'
    }
    return typeMap[type] || '-'
}

/**
 * 切换全屏状态
 */
const toggleFullscreen = () => {
    isFullscreen.value = !isFullscreen.value
}

/**
 * 展示下拉图标
 */
const showSelectIcon = () => {
    iconSelectRef.value?.reset()
}

/**
 * 选择图标
 */
const selected = (name) => {
    formData.value.icon = name
}

/**
 * 处理字段变更
 */
const handleFieldChange = (field, value) => {
    // 字段变更处理逻辑
}

/**
 * 对外暴露的打开方法
 */
const openDialog = async (type, title, data = {}) => {
    dialogType.value = type
    dialogTitle.value = title

    // 初始化配置
    await initializeConfig()

    // 获取菜单树数据
    await getTreeselect()

    if (type === 'add') {
        // 新增模式 - 使用默认值
        formData.value = {
            menuId: undefined,
            parentId: props.parentMenuId || 0,
            menuName: undefined,
            icon: undefined,
            menuType: "M",
            orderNum: 0,
            isFrame: "1",
            isCache: "0",
            visible: "0",
            status: "0"
        }
    } else if (type === 'edit' || type === 'view') {
        // 编辑/查看模式 - 获取详情数据
        if (data.menuId) {
            try {
                const response = await getMenu(data.menuId)
                if (response && response.data) {
                    formData.value = { ...response.data }
                } else {
                    throw new Error('获取菜单详情失败')
                }
            } catch (error) {
                console.error('获取菜单详情失败:', error)
                proxy?.$modal?.msgError(error.message || '获取菜单详情失败')
                return
            }
        } else {
            formData.value = { ...data }
        }
    }

    dialogVisible.value = true
}

/**
 * 关闭弹窗
 */
const closeDialog = () => {
    dialogVisible.value = false
}

/**
 * 处理表单提交
 */
const handleSubmitForm = async () => {
    if (submitLoading.value || submitDisabled.value) {
        return
    }

    if (!formListRef.value) {
        return
    }

    try {
        submitLoading.value = true
        submitDisabled.value = true

        // 表单验证
        await formListRef.value.validate()

        // 发送提交事件给父组件
        emit('submit', {
            type: dialogType.value,
            data: formData.value
        })

    } catch (error) {
        submitLoading.value = false
        submitDisabled.value = false
    }
}

/**
 * 处理取消
 */
const handleCancel = () => {
    emit('cancel')
    closeDialog()
}

/**
 * 处理表单对话框打开
 */
const handleDialogOpened = () => {
    submitLoading.value = false
    submitDisabled.value = false
}

/**
 * 处理表单对话框关闭
 */
const handleDialogClosed = () => {
    formData.value = {}
    menuOptions.value = []
    submitLoading.value = false
    submitDisabled.value = false
}

/**
 * 提交成功后的回调
 */
const onSubmitSuccess = () => {
    submitLoading.value = false
    submitDisabled.value = false
    closeDialog()
}

/**
 * 提交失败后的回调
 */
const onSubmitError = () => {
    submitLoading.value = false
    submitDisabled.value = false
}

// 暴露给父组件的方法
defineExpose({
    openDialog,
    closeDialog,
    onSubmitSuccess,
    onSubmitError
})
</script>

<style lang="scss" scoped>
.custom-dialog-btn {
    padding: 8px 20px;
}

.dialog-content {
    max-height: v-bind("dialogContentStyle.maxHeight");
    min-height: v-bind("dialogContentStyle.minHeight || 'auto'");
    overflow-y: v-bind("dialogContentStyle.overflowY");
    overflow-x: hidden !important;
    padding: v-bind("dialogContentStyle.padding");
    width: 100%;
    box-sizing: border-box;

    &.view-mode {
        padding: 16px 10px;
        width: 100%;
        box-sizing: border-box;
        overflow-x: hidden !important;
    }
}

:deep(.el-divider) {
    margin: 20px 0 15px 0;
}

:deep(.el-divider--horizontal) {
    margin: 20px 0 15px 0;
}

:deep(.el-form-item) {
    margin-bottom: 18px;
}

:deep(.el-input-number) {
    width: 100%;
}
</style>