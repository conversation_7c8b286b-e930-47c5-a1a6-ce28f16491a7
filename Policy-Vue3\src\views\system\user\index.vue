<template>
  <div class="user-container app-container">
    <!-- 使用 TableList 组件 -->
    <TableList v-if="isTableReady" :columns="tableColumns" :data="userList" :loading="tableLoading" :showIndex="true"
      :searchColumns="searchableColumns" :showOperation="true" operationLabel="操作" operationWidth="230"
      :fixedOperation="true" ref="tableListRef" @search="handleSearch" @reset="resetSearch"
      :defaultPage="{ pageSize: queryParams.pageSize, currentPage: queryParams.pageNum, total: total }"
      @current-change="handleCurrentChange" @size-change="handleSizeChange" @selection-change="handleSelectionChange">

      <!-- 左侧按钮插槽 -->
      <template #menu-left>
        <el-button type="primary" class="custom-btn" @click="handleAdd" v-hasPermi="['system:user:add']">新 增</el-button>
        <el-button type="info" plain class="custom-btn" @click="handleImport" v-hasPermi="['system:user:import']">导
          入</el-button>
        <el-button type="warning" plain class="custom-btn" @click="handleExport" v-hasPermi="['system:user:export']">导
          出</el-button>
      </template>

      <!-- 自定义搜索插槽 - 部门选择 -->
      <template #search-deptName>
        <el-tree-select v-model="searchParams.deptId" :data="deptOptions"
          :props="{ value: 'id', label: 'label', children: 'children' }" value-key="id" placeholder="请选择部门"
          check-strictly clearable class="search-form-item" />
      </template>

      <!-- 状态列插槽 -->
      <template #status="{ row }">
        <el-switch v-model="row.status" active-value="0" inactive-value="1"
          @change="handleStatusChange(row)"></el-switch>
      </template>

      <!-- 操作列插槽 -->
      <template #menu="{ row }">
        <div class="operation-btns">
          <el-button type="primary" link @click="handleView(row)">查看</el-button>
          <el-button v-if="row.userId !== 1" type="primary" link @click="handleUpdate(row)"
            v-hasPermi="['system:user:edit']">编辑</el-button>
          <el-button v-if="row.userId !== 1" type="danger" link @click="handleDelete(row)"
            v-hasPermi="['system:user:remove']">删除</el-button>
          <el-dropdown v-if="row.userId !== 1" @command="handleCommand($event, row)">
            <el-button type="primary" link>
              更多<el-icon class="el-icon--right"><arrow-down /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item command="handleResetPwd">
                  <span v-hasPermi="['system:user:resetPwd']">重置密码</span>
                </el-dropdown-item>
                <el-dropdown-item command="handleAuthRole">
                  <span v-hasPermi="['system:user:edit']">分配角色</span>
                </el-dropdown-item>
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </template>
    </TableList>
    <div v-else class="loading-placeholder">
      <el-empty description="正在加载表格配置..."></el-empty>
    </div>

    <!-- 表单弹窗组件 -->
    <UserFormDialog ref="userFormDialogRef" :formFields="formFields" :formOption="formOption"
      :deptOptions="enabledDeptOptions" :postOptions="postOptions" :roleOptions="roleOptions" @submit="handleFormSubmit"
      @cancel="handleFormCancel" />

    <!-- 用户导入对话框 -->
    <el-dialog :title="upload.title" v-model="upload.open" width="400px" append-to-body>
      <el-upload ref="uploadRef" :limit="1" accept=".xlsx, .xls" :headers="upload.headers"
        :action="upload.url + '?updateSupport=' + upload.updateSupport" :disabled="upload.isUploading"
        :on-progress="handleFileUploadProgress" :on-success="handleFileSuccess" :auto-upload="false" drag>
        <el-icon class="el-icon--upload"><upload-filled /></el-icon>
        <div class="el-upload__text">将文件拖到此处，或<em>点击上传</em></div>
        <template #tip>
          <div class="el-upload__tip text-center">
            <div class="el-upload__tip">
              <el-checkbox v-model="upload.updateSupport" />是否更新已经存在的用户数据
            </div>
            <span>仅允许导入xls、xlsx格式文件。</span>
            <el-link type="primary" :underline="false" style="font-size: 12px; vertical-align: baseline"
              @click="importTemplate">下载模板</el-link>
          </div>
        </template>
      </el-upload>
      <template #footer>
        <div class="dialog-footer">
          <el-button type="primary" class="common-btn" @click="submitFileForm">确 定</el-button>
          <el-button class="common-btn" @click="upload.open = false">取 消</el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup name="User">
import { getToken } from "@/utils/auth"
import useAppStore from '@/store/modules/app'
import { changeUserStatus, listUser, resetUserPwd, delUser, getUser, updateUser, addUser, deptTreeSelect } from "@/api/system/user"
import { createUserTableOption } from "@/const/system/user"
import { getCoSyncColumn, extractTableColumns } from "@/utils/columnUtils"
import { Splitpanes, Pane } from "splitpanes"
import "splitpanes/dist/splitpanes.css"
import TableList from '@/components/TableList/index.vue'
import UserFormDialog from './UserFormDialog.vue'
import { ArrowDown } from '@element-plus/icons-vue'

const router = useRouter()
const appStore = useAppStore()
const { proxy } = getCurrentInstance()
const { sys_normal_disable, sys_user_sex } = proxy.useDict("sys_normal_disable", "sys_user_sex")

// 注册图标组件
const components = {
  ArrowDown
}

const userList = ref([])
const loading = ref(true)
const ids = ref([])
const single = ref(true)
const multiple = ref(true)
const total = ref(0)
const deptName = ref("")
const deptOptions = ref(undefined)
const enabledDeptOptions = ref(undefined)
const initPassword = ref(undefined)
const postOptions = ref([])
const roleOptions = ref([])
const isInitializing = ref(true) // 添加初始化标志

// 新的封装组件相关变量
const tableColumns = ref([])
const searchableColumns = ref([]) // 可搜索的字段列表
const tableLoading = ref(false)
const isTableReady = ref(false)
const formOption = ref({
  dialogWidth: '800px',
  dialogHeight: '60vh'
})
const tableListRef = ref(null)
const userFormDialogRef = ref(null)
const formFields = ref([])
const searchParams = ref({})
/*** 用户导入参数 */
const upload = reactive({
  // 是否显示弹出层（用户导入）
  open: false,
  // 弹出层标题（用户导入）
  title: "",
  // 是否禁用上传
  isUploading: false,
  // 是否更新已经存在的用户数据
  updateSupport: 0,
  // 设置上传的请求头部
  headers: { 'ADMIN-Authorization': "Bearer " + getToken() },
  // 上传的地址
  url: import.meta.env.VITE_APP_BASE_API + "/system/user/importData"
})
// 列显隐信息
const columns = ref([
  { key: 0, label: `用户编号`, visible: true },
  { key: 1, label: `用户名称`, visible: true },
  { key: 2, label: `用户昵称`, visible: true },
  { key: 3, label: `部门`, visible: true },
  { key: 4, label: `手机号码`, visible: true },
  { key: 5, label: `状态`, visible: true },
  { key: 6, label: `创建时间`, visible: true }
])

const data = reactive({
  queryParams: {
    pageNum: 1,
    pageSize: 10,
    userName: undefined,
    phonenumber: undefined,
    status: undefined,
    deptId: undefined,
    email: undefined,
    sex: undefined,
    userType: undefined
  }
})

const { queryParams } = toRefs(data)

// 初始化配置
onMounted(async () => {
  await initializeConfig()
  getDeptTree()
  getList()
  proxy.getConfigKey("sys.user.initPassword").then(response => {
    initPassword.value = response.msg
  })
})

// 初始化配置
const initializeConfig = async () => {
  try {
    // 获取基础配置
    const baseOption = createUserTableOption(proxy);

    // 使用工具类获取合并后的配置
    const mergedConfig = await getCoSyncColumn({
      baseOption,
      proxy
    });

    // 使用工具类提取完整配置 - 包含表格列、搜索字段、表单字段和表单选项
    const { tableColumns: extractedTableColumns, searchColumns, formFields: extractedFormFields, formOptions } = extractTableColumns(mergedConfig);

    // 设置表格和搜索配置
    tableColumns.value = extractedTableColumns;
    searchableColumns.value = searchColumns;

    // 设置表单字段配置
    formFields.value = extractedFormFields;

    // 设置表单选项配置 - 直接使用 extractTableColumns 返回的完整配置
    formOption.value = {
      ...formOption.value, // 保留默认配置
      ...formOptions       // 使用从配置文件中提取的完整选项
    };

    isTableReady.value = true;
  } catch (error) {
    isTableReady.value = false;
    console.error('初始化配置失败:', error);
  }
};



/** 通过条件过滤节点  */
const filterNode = (value, data) => {
  if (!value) return true
  return data.label.indexOf(value) !== -1
}

/** 根据名称筛选部门树 */
watch(deptName, val => {
  proxy.$refs["deptTreeRef"].filter(val)
})

/** 查询用户列表 */
function getList() {
  tableLoading.value = true
  loading.value = true
  // 处理日期范围搜索参数
  let params = { ...queryParams.value }
  if (searchParams.value.createTime && Array.isArray(searchParams.value.createTime) && searchParams.value.createTime.length === 2) {
    params = proxy.addDateRange(params, searchParams.value.createTime)
  }

  listUser(params).then(res => {
    tableLoading.value = false
    loading.value = false
    userList.value = res.rows
    total.value = res.total

    // 数据加载完成后，设置初始化完成
    nextTick(() => {
      isInitializing.value = false
    })
  })
}

/** 查询部门下拉树结构 */
function getDeptTree() {
  deptTreeSelect().then(response => {
    deptOptions.value = response.data
    enabledDeptOptions.value = filterDisabledDept(JSON.parse(JSON.stringify(response.data)))
  })
}

/** 过滤禁用的部门 */
function filterDisabledDept(deptList) {
  return deptList.filter(dept => {
    if (dept.disabled) {
      return false
    }
    if (dept.children && dept.children.length) {
      dept.children = filterDisabledDept(dept.children)
    }
    return true
  })
}

/** 节点单击事件 */
function handleNodeClick(data) {
  queryParams.value.deptId = data.id
  handleQuery()
}

// 处理搜索
const handleSearch = (params) => {
  // 标记为初始化状态，防止状态开关误触发
  isInitializing.value = true;

  // 保存搜索参数（包括日期范围）
  searchParams.value = { ...params };

  // 合并搜索参数到queryParams（排除日期范围，因为API需要特殊处理）
  const { createTime, ...otherParams } = params || {};
  Object.assign(queryParams.value, otherParams);
  queryParams.value.pageNum = 1;
  getList();
};

// 重置搜索
const resetSearch = () => {
  // 标记为初始化状态，防止状态开关误触发
  isInitializing.value = true;

  queryParams.value = {
    pageNum: 1,
    pageSize: 10,
    userName: undefined,
    phonenumber: undefined,
    status: undefined,
    deptId: undefined,
    email: undefined,
    sex: undefined,
    userType: undefined
  };
  searchParams.value = {};
  // 清除部门树选择
  if (proxy.$refs.deptTreeRef) {
    proxy.$refs.deptTreeRef.setCurrentKey(null);
  }
  getList();
};

// 处理页码变化
const handleCurrentChange = (page) => {
  isInitializing.value = true;
  queryParams.value.pageNum = page;
  getList();
};

// 处理每页条数变化
const handleSizeChange = (size) => {
  isInitializing.value = true;
  queryParams.value.pageSize = size;
  queryParams.value.pageNum = 1;
  getList();
};

/** 搜索按钮操作 */
function handleQuery() {
  queryParams.value.pageNum = 1
  getList()
}

/** 重置按钮操作 */
function resetQuery() {
  queryParams.value.deptId = undefined
  proxy.$refs.deptTreeRef.setCurrentKey(null)
  handleQuery()
}

/** 删除按钮操作 */
function handleDelete(row) {
  const userIds = row.userId || ids.value
  proxy.$modal.confirm('是否确认删除用户编号为"' + userIds + '"的数据项？').then(function () {
    return delUser(userIds)
  }).then(() => {
    getList()
    proxy.$modal.msgSuccess("删除成功")
  }).catch(() => { })
}

/** 导出按钮操作 */
function handleExport() {
  proxy.download("system/user/export", {
    ...queryParams.value,
  }, `user_${new Date().getTime()}.xlsx`)
}

/** 用户状态修改  */
function handleStatusChange(row) {
  // 如果正在初始化，则忽略状态变更事件
  if (isInitializing.value) {
    return
  }

  let text = row.status === "0" ? "启用" : "停用"
  proxy.$modal.confirm('确认要"' + text + '""' + row.userName + '"用户吗?').then(function () {
    return changeUserStatus(row.userId, row.status)
  }).then(() => {
    proxy.$modal.msgSuccess(text + "成功")
  }).catch(function () {
    row.status = row.status === "0" ? "1" : "0"
  })
}

/** 更多操作 */
function handleCommand(command, row) {
  switch (command) {
    case "handleResetPwd":
      handleResetPwd(row)
      break
    case "handleAuthRole":
      handleAuthRole(row)
      break
    default:
      break
  }
}

/** 跳转角色分配 */
function handleAuthRole(row) {
  const userId = row.userId
  router.push("/system/user-auth/role/" + userId)
}

/** 重置密码按钮操作 */
function handleResetPwd(row) {
  proxy.$prompt('请输入"' + row.userName + '"的新密码', "提示", {
    confirmButtonText: "确定",
    cancelButtonText: "取消",
    closeOnClickModal: false,
    inputPattern: /^.{5,20}$/,
    inputErrorMessage: "用户密码长度必须介于 5 和 20 之间",
    inputValidator: (value) => {
      if (/<|>|"|'|\||\\/.test(value)) {
        return "不能包含非法字符：< > \" ' \\\ |"
      }
    },
  }).then(({ value }) => {
    resetUserPwd(row.userId, value).then(response => {
      proxy.$modal.msgSuccess("修改成功，新密码是：" + value)
    })
  }).catch(() => { })
}

/** 选择条数  */
function handleSelectionChange(selection) {
  ids.value = selection.map(item => item.userId)
  single.value = selection.length != 1
  multiple.value = !selection.length
}

/** 导入按钮操作 */
function handleImport() {
  upload.title = "用户导入"
  upload.open = true
}

/** 下载模板操作 */
function importTemplate() {
  proxy.download("system/user/importTemplate", {
  }, `user_template_${new Date().getTime()}.xlsx`)
}

/**文件上传中处理 */
const handleFileUploadProgress = (event, file, fileList) => {
  upload.isUploading = true
}

/** 文件上传成功处理 */
const handleFileSuccess = (response, file, fileList) => {
  upload.open = false
  upload.isUploading = false
  proxy.$refs["uploadRef"].handleRemove(file)
  proxy.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>" + response.msg + "</div>", "导入结果", { dangerouslyUseHTMLString: true })
  getList()
}

/** 提交上传文件 */
function submitFileForm() {
  proxy.$refs["uploadRef"].submit()
}



// 查看
const handleView = (row) => {
  userFormDialogRef.value?.openDialog('view', '查看用户', row)
}

// 编辑
const handleEdit = (row) => {
  const userId = row.userId
  getUser(userId).then(response => {
    const userData = {
      ...response.data,
      postIds: response.postIds,
      roleIds: response.roleIds
    }
    postOptions.value = response.posts
    roleOptions.value = response.roles
    userFormDialogRef.value?.openDialog('edit', '编辑用户', userData)
  })
}

// 新增
const handleAddUser = () => {
  getUser().then(response => {
    postOptions.value = response.posts
    roleOptions.value = response.roles
    const defaultData = {
      password: initPassword.value,
      status: "0",
      postIds: [],
      roleIds: []
    }
    userFormDialogRef.value?.openDialog('add', '新增用户', defaultData)
  })
}

// 处理表单提交事件
const handleFormSubmit = async (payload) => {
  try {
    if (payload.type === 'add') {
      // 新增
      await addUser(payload.data)
      proxy.$modal.msgSuccess("添加成功")
    } else if (payload.type === 'edit') {
      // 编辑
      await updateUser(payload.data)
      proxy.$modal.msgSuccess("修改成功")
    }

    // 通知子组件提交成功
    userFormDialogRef.value?.onSubmitSuccess()
    getList()
  } catch (error) {
    // 通知子组件提交失败
    userFormDialogRef.value?.onSubmitError()
    console.error('提交失败:', error)
  }
}

// 处理表单取消事件
const handleFormCancel = () => {
  // 可以在这里添加取消逻辑
}

/** 新增按钮操作 */
function handleAdd() {
  handleAddUser()
}

/** 修改按钮操作 */
function handleUpdate(row) {
  if (row) {
    handleEdit(row)
  } else {
    // 批量编辑
    const userId = ids.value[0]
    const selectedRow = userList.value.find(item => item.userId === userId)
    if (selectedRow) {
      handleEdit(selectedRow)
    }
  }
}


</script>

<style lang="scss" scoped></style>
