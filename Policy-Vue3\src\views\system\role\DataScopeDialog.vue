<template>
    <el-dialog :title="dialogTitle" v-model="dialogVisible" width="500px" append-to-body>
        <el-form :model="formData" label-width="80px">
            <el-form-item label="角色名称">
                <el-input v-model="formData.roleName" :disabled="true" />
            </el-form-item>
            <el-form-item label="权限字符">
                <el-input v-model="formData.roleKey" :disabled="true" />
            </el-form-item>
            <el-form-item label="权限范围">
                <el-select v-model="formData.dataScope" @change="dataScopeSelectChange">
                    <el-option v-for="item in dataScopeOptions" :key="item.value" :label="item.label"
                        :value="item.value"></el-option>
                </el-select>
            </el-form-item>
            <el-form-item label="数据权限" v-show="formData.dataScope == 2">
                <div class="tree-controls">
                    <el-checkbox v-model="deptExpand"
                        @change="handleCheckedTreeExpand($event, 'dept')">展开/折叠</el-checkbox>
                    <el-checkbox v-model="deptNodeAll"
                        @change="handleCheckedTreeNodeAll($event, 'dept')">全选/全不选</el-checkbox>
                    <el-checkbox v-model="formData.deptCheckStrictly"
                        @change="handleCheckedTreeConnect($event, 'dept')">父子联动</el-checkbox>
                </div>
                <el-tree class="tree-border" :data="deptOptions" show-checkbox default-expand-all ref="deptRef"
                    node-key="id" :check-strictly="!formData.deptCheckStrictly" empty-text="加载中，请稍候"
                    :props="{ label: 'label', children: 'children' }"></el-tree>
            </el-form-item>
        </el-form>
        <template #footer>
            <div class="dialog-footer">
                <el-button type="primary" class="common-btn" @click="handleSubmit">确 定</el-button>
                <el-button class="common-btn" @click="handleCancel">取 消</el-button>
            </div>
        </template>
    </el-dialog>
</template>

<script setup name="DataScopeDialog">
import { ref, reactive, getCurrentInstance, nextTick } from 'vue';
import { deptTreeSelect } from "@/api/system/role";

const { proxy } = getCurrentInstance();

// 数据权限选项
const dataScopeOptions = ref([
    { value: "1", label: "全部数据权限" },
    { value: "2", label: "自定数据权限" },
    { value: "3", label: "本部门数据权限" },
    { value: "4", label: "本部门及以下数据权限" },
    { value: "5", label: "仅本人数据权限" }
]);

// Emits
const emit = defineEmits([
    'submit',
    'cancel'
]);

// 数据定义
const dialogVisible = ref(false);
const dialogTitle = ref('分配数据权限');
const formData = ref({});
const deptRef = ref(null);
const deptOptions = ref([]);
const deptExpand = ref(true);
const deptNodeAll = ref(false);

// 对外暴露的方法
const openDialog = async (roleData) => {
    formData.value = {
        ...roleData,
        deptCheckStrictly: true
    };

    // 重置树状态
    deptExpand.value = true;
    deptNodeAll.value = false;

    // 获取部门树数据
    await getDeptTree(roleData.roleId);

    dialogVisible.value = true;
};

const closeDialog = () => {
    dialogVisible.value = false;
};

// 根据角色ID查询部门树结构
const getDeptTree = async (roleId) => {
    try {
        const response = await deptTreeSelect(roleId);
        deptOptions.value = response.depts;

        // 设置已选中的部门
        nextTick(() => {
            const checkedKeys = response.checkedKeys;
            checkedKeys.forEach((v) => {
                nextTick(() => {
                    if (deptRef.value) {
                        deptRef.value.setChecked(v, true, false);
                    }
                });
            });
        });

        return response;
    } catch (error) {
        console.error('获取部门树失败:', error);
    }
};

// 数据权限范围变更
const dataScopeSelectChange = (value) => {
    if (value === '2') {
        getDeptTree(formData.value.roleId);
    } else {
        // 清空部门选择
        if (deptRef.value) {
            deptRef.value.setCheckedKeys([]);
        }
    }
};

// 树权限（展开/折叠）
const handleCheckedTreeExpand = (value, type) => {
    if (type === "dept") {
        let treeList = deptOptions.value;
        for (let i = 0; i < treeList.length; i++) {
            if (deptRef.value?.store.nodesMap[treeList[i].id]) {
                deptRef.value.store.nodesMap[treeList[i].id].expanded = value;
            }
        }
    }
};

// 树权限（全选/全不选）
const handleCheckedTreeNodeAll = (value, type) => {
    if (type === "dept") {
        deptRef.value?.setCheckedNodes(value ? deptOptions.value : []);
    }
};

// 树权限（父子联动）
const handleCheckedTreeConnect = (value, type) => {
    if (type === "dept") {
        formData.value.deptCheckStrictly = value ? true : false;
    }
};

// 所有部门节点数据
const getDeptAllCheckedKeys = () => {
    // 目前被选中的部门节点
    let checkedKeys = deptRef.value?.getCheckedKeys() || [];
    // 半选中的部门节点
    let halfCheckedKeys = deptRef.value?.getHalfCheckedKeys() || [];
    checkedKeys.unshift.apply(checkedKeys, halfCheckedKeys);
    return checkedKeys;
};

// 提交
const handleSubmit = () => {
    // 如果是自定义数据权限，获取部门权限
    if (formData.value.dataScope === '2') {
        formData.value.deptIds = getDeptAllCheckedKeys();
    }

    emit('submit', formData.value);
    closeDialog();
};

// 取消
const handleCancel = () => {
    emit('cancel');
    closeDialog();
};

// 暴露给父组件的方法
defineExpose({
    openDialog,
    closeDialog
});
</script>

<style lang="scss" scoped>
.tree-controls {
    margin-bottom: 10px;

    .el-checkbox {
        margin-right: 15px;
    }
}

.tree-border {
    margin-top: 5px;
    border: 1px solid #e5e6e7;
    background: #ffffff none;
    border-radius: 4px;
    width: 100%;
    max-height: 300px;
    overflow-y: auto;
    padding: 10px;
}
</style>