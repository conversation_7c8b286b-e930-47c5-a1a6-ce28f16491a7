import { parseTime } from "@/utils/ruoyi";

export const createPolicyApplicationTableOption = (proxy) => {
    // 申请状态选项
    const applicationStatusOptions = [
        { label: "待初审", value: "0", color: "warning" },
        { label: "初审通过", value: "1", color: "success" },
        { label: "初审拒绝", value: "2", color: "danger" },
        { label: "待终审", value: "3", color: "warning" },
        { label: "终审通过", value: "4", color: "success" },
        { label: "终审拒绝", value: "5", color: "danger" },
        { label: "已完成", value: "6", color: "info" }
    ];

    // 政策类型选项
    const policyTypeOptions = [
        { label: "就业扶持", value: "就业扶持" },
        { label: "创业支持", value: "创业支持" },
        { label: "技能培训", value: "技能培训" },
        { label: "社会保障", value: "社会保障" },
        { label: "其他", value: "其他" }
    ];

    return {
        dialogWidth: '1000px',  // 弹窗宽度
        dialogHeight: '70vh',   // 弹窗内容区最大高度
        labelWidth: '120px',
        column: [
            // ==================== 申请基础信息分组 ====================
            {
                label: "申请基础信息",
                prop: "divider_basic_info",
                formSlot: true,
                headerAlign: "left",
                labelWidth: 0,
                span: 24,
                divider: true
            },
            {
                label: "政策名称",
                prop: "policyName",
                search: true,
                searchSpan: 8,
                minWidth: 200,
                addDisplay: false,
                editDisplay: false,
                viewDisplay: true
            },
            {
                label: "政策类型",
                prop: "policyType",
                search: true,
                searchSpan: 8,
                width: 120,
                align: "center",
                type: "select",
                dicData: policyTypeOptions,
                addDisplay: false,
                editDisplay: false,
                viewDisplay: true
            },
            {
                label: "姓名",
                prop: "applicantName",
                search: true,
                searchSpan: 8,
                width: 120,
                align: "center",
                addDisplay: false,
                editDisplay: false,
                viewDisplay: true
            },
            {
                label: "手机号",
                prop: "applicantPhone",
                searchSpan: 8,
                width: 120,
                align: "center",
                addDisplay: false,
                editDisplay: false,
                viewDisplay: true
            },
            {
                label: "申请人",
                prop: "applicantUserName",
                search: true,
                searchSpan: 8,
                width: 120,
                align: "center",
                addDisplay: false,
                editDisplay: false,
                viewDisplay: true
            },
            {
                label: "申请状态",
                prop: "applicationStatus",
                search: true,
                searchSpan: 8,
                width: 120,
                align: "center",
                type: "select",
                dicData: applicationStatusOptions,
                slot: true,
                addDisplay: false,
                editDisplay: false,
                viewDisplay: true
            },
            {
                label: "提交时间",
                prop: "submitTime",
                searchSpan: 8,
                width: 160,
                align: "center",
                type: "datetime",
                format: "YYYY-MM-DD HH:mm:ss",
                valueFormat: "YYYY-MM-DD HH:mm:ss",
                addDisplay: false,
                editDisplay: false,
                viewDisplay: true,
                formatter: (row) => {
                    return parseTime(row.submitTime);
                }
            },
            {
                label: "完成时间",
                prop: "completeTime",
                search: false,
                width: 160,
                align: "center",
                addDisplay: false,
                editDisplay: false,
                viewDisplay: true,
                formatter: (row) => {
                    return row.completeTime ? parseTime(row.completeTime) : '-';
                }
            },
            // ==================== 申请材料信息分组 ====================
            {
                label: "申请材料信息",
                prop: "divider_materials_info",
                formSlot: true,
                headerAlign: "left",
                labelWidth: 0,
                span: 24,
                divider: true,
                viewDisplay: true,
                addDisplay: false,
                editDisplay: false
            },
            // ==================== 系统信息分组 ====================
            {
                label: "系统信息",
                prop: "divider_system_info",
                formSlot: true,
                headerAlign: "left",
                labelWidth: 0,
                span: 24,
                divider: true,
                viewDisplay: true,
                addDisplay: false,
                editDisplay: false
            },
            {
                label: "备注",
                prop: "remark",
                search: false,
                type: "textarea",
                span: 24,
                minRows: 2,
                maxRows: 4,
                readonly: true,
                addDisplay: false,
                editDisplay: false,
                viewDisplay: true
            }
        ]
    };
};

// 审核操作配置
export const createReviewFormOption = () => {
    return {
        dialogWidth: '600px',
        dialogHeight: '50vh',
        labelWidth: '100px',
        column: [
            {
                label: "审核结果",
                prop: "approvalStatus",
                type: "radio",
                dicData: [
                    { label: "通过", value: "1" },
                    { label: "拒绝", value: "2" }
                ],
                rules: [
                    { required: true, message: "请选择审核结果", trigger: "change" }
                ],
                span: 24
            },
            {
                label: "审核意见",
                prop: "approvalComment",
                type: "textarea",
                span: 24,
                minRows: 4,
                maxRows: 8,
                showWordLimit: true,
                maxlength: 1000,
                rules: [
                    { required: true, message: "请填写审核意见", trigger: "blur" },
                    { max: 1000, message: "审核意见不能超过1000个字符", trigger: "blur" }
                ]
            }
        ]
    };
};
