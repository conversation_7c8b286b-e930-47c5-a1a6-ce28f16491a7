<template>
  <el-dialog
    v-model="dialogVisible"
    :title="dialogTitle"
    :width="formOption.dialogWidth || '1000px'"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    append-to-body
  >
    <div class="form-container" :style="{ maxHeight: formOption.dialogHeight || '70vh' }">
      <el-form
        ref="formRef"
        :model="formData"
        :rules="formRules"
        :label-width="formOption.labelWidth || '120px'"
        class="form-content"
      >
        <el-row :gutter="20">
          <template v-for="field in formFields" :key="field.prop">
            <!-- 分隔线 -->
            <el-col v-if="field.divider" :span="24" class="divider-col">
              <el-divider content-position="left">
                <span class="divider-text">{{ field.label }}</span>
              </el-divider>
            </el-col>
            
            <!-- 普通字段 -->
            <el-col v-else :span="field.span || 12">
              <el-form-item
                :label="field.label"
                :prop="field.prop"
                :rules="field.rules"
                v-if="shouldShowField(field)"
              >
                <!-- 文本输入框 -->
                <el-input
                  v-if="field.type === 'input' || !field.type"
                  v-model="formData[field.prop]"
                  :placeholder="field.placeholder || `请输入${field.label}`"
                  :disabled="isViewMode || field.readonly"
                  :maxlength="field.maxlength"
                  :show-word-limit="field.showWordLimit"
                />
                
                <!-- 文本域 -->
                <el-input
                  v-else-if="field.type === 'textarea'"
                  v-model="formData[field.prop]"
                  type="textarea"
                  :rows="field.minRows || 4"
                  :maxlength="field.maxlength"
                  :show-word-limit="field.showWordLimit"
                  :placeholder="field.placeholder || `请输入${field.label}`"
                  :disabled="isViewMode || field.readonly"
                />
                
                <!-- 选择器 -->
                <el-select
                  v-else-if="field.type === 'select'"
                  v-model="formData[field.prop]"
                  :placeholder="field.placeholder || `请选择${field.label}`"
                  :disabled="isViewMode || field.readonly"
                  style="width: 100%"
                >
                  <el-option
                    v-for="option in field.dicData"
                    :key="option.value"
                    :label="option.label"
                    :value="option.value"
                  />
                </el-select>
                
                <!-- 日期时间选择器 -->
                <el-date-picker
                  v-else-if="field.type === 'datetime'"
                  v-model="formData[field.prop]"
                  type="datetime"
                  :placeholder="field.placeholder || `请选择${field.label}`"
                  :disabled="isViewMode || field.readonly"
                  :format="field.format || 'YYYY-MM-DD HH:mm:ss'"
                  :value-format="field.valueFormat || 'YYYY-MM-DD HH:mm:ss'"
                  style="width: 100%"
                />
                
                <!-- 数字输入框 -->
                <el-input-number
                  v-else-if="field.type === 'number'"
                  v-model="formData[field.prop]"
                  :min="field.min"
                  :max="field.max"
                  :step="field.step || 1"
                  :disabled="isViewMode || field.readonly"
                  style="width: 100%"
                />
                
                <!-- 开关 -->
                <el-switch
                  v-else-if="field.type === 'switch'"
                  v-model="formData[field.prop]"
                  :disabled="isViewMode || field.readonly"
                  :active-value="field.activeValue || true"
                  :inactive-value="field.inactiveValue || false"
                />
                
                <!-- 默认文本显示 -->
                <span v-else class="form-text">
                  {{ formData[field.prop] || '-' }}
                </span>
              </el-form-item>
            </el-col>
          </template>
        </el-row>
      </el-form>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="handleCancel">{{ isViewMode ? '关闭' : '取消' }}</el-button>
        <el-button
          v-if="!isViewMode"
          type="primary"
          :loading="submitLoading"
          @click="handleSubmit"
        >
          确定
        </el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup>
import { ref, reactive, watch, computed, getCurrentInstance } from 'vue'

const { proxy } = getCurrentInstance()
const emit = defineEmits(['submit', 'cancel'])

const props = defineProps({
  formFields: {
    type: Array,
    default: () => []
  },
  formOption: {
    type: Object,
    default: () => ({})
  }
})

const dialogVisible = ref(false)
const dialogTitle = ref('')
const dialogType = ref('')
const formData = ref({})
const formRules = ref({})
const submitLoading = ref(false)
const formRef = ref(null)

// 计算是否为查看模式
const isViewMode = computed(() => dialogType.value === 'view')

// 监听表单字段变化，生成验证规则
watch(() => props.formFields, (newFields) => {
  const rules = {}
  newFields.forEach(field => {
    if (field.rules && field.prop) {
      rules[field.prop] = field.rules
    }
  })
  formRules.value = rules
}, { immediate: true, deep: true })

// 打开弹窗
const openDialog = (type, title, data = {}) => {
  dialogType.value = type
  dialogTitle.value = title
  formData.value = { ...data }
  dialogVisible.value = true
  
  // 重置表单验证状态
  if (formRef.value) {
    formRef.value.clearValidate()
  }
}

// 判断字段是否应该显示
const shouldShowField = (field) => {
  if (dialogType.value === 'add') {
    return field.addDisplay !== false
  } else if (dialogType.value === 'edit') {
    return field.editDisplay !== false
  } else if (dialogType.value === 'view') {
    return field.viewDisplay !== false
  }
  return true
}

// 提交表单
const handleSubmit = async () => {
  if (!formRef.value) return
  
  try {
    await formRef.value.validate()
    
    submitLoading.value = true
    
    const payload = {
      type: dialogType.value,
      data: { ...formData.value }
    }
    
    emit('submit', payload)
  } catch (error) {
    console.error('表单验证失败:', error)
  }
}

// 提交成功回调
const onSubmitSuccess = () => {
  submitLoading.value = false
  dialogVisible.value = false
  resetForm()
}

// 提交失败回调
const onSubmitError = () => {
  submitLoading.value = false
}

// 取消操作
const handleCancel = () => {
  dialogVisible.value = false
  resetForm()
  emit('cancel')
}

// 重置表单
const resetForm = () => {
  formData.value = {}
  if (formRef.value) {
    formRef.value.resetFields()
  }
}

// 暴露方法给父组件
defineExpose({
  openDialog,
  onSubmitSuccess,
  onSubmitError
})
</script>

<style scoped>
.form-container {
  overflow-y: auto;
  padding-right: 8px;
}

.form-content {
  padding: 0 16px;
}

.divider-col {
  margin: 16px 0;
}

.divider-text {
  font-weight: 500;
  color: #303133;
  font-size: 14px;
}

.form-text {
  color: #606266;
  line-height: 32px;
}

.dialog-footer {
  text-align: right;
  padding-top: 16px;
  border-top: 1px solid #ebeef5;
}

:deep(.el-form-item__label) {
  font-weight: 500;
  color: #606266;
}

:deep(.el-divider__text) {
  background-color: #fff;
  padding: 0 16px;
}

:deep(.el-divider--horizontal) {
  margin: 16px 0;
}

/* 滚动条样式 */
.form-container::-webkit-scrollbar {
  width: 6px;
}

.form-container::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3px;
}

.form-container::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

.form-container::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
