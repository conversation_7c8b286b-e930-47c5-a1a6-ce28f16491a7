import { parseTime } from "@/utils/ruoyi";

export const createWorkerProfileTableOption = (proxy) => {
    const {
        sys_normal_disable,
        worker_status_options,
        gender_options,
        education_level_options,
        work_time_preference_options,
        salary_type_options
    } = proxy.useDict(
        "sys_normal_disable",
        "worker_status_options",
        "gender_options", 
        "education_level_options",
        "work_time_preference_options",
        "salary_type_options"
    );

    // 性别选项
    const genderOptions = [
        { label: "男", value: "male" },
        { label: "女", value: "female" }
    ];

    // 学历选项
    const educationLevelOptions = [
        { label: "小学", value: "小学" },
        { label: "初中", value: "初中" },
        { label: "高中", value: "高中" },
        { label: "中专", value: "中专" },
        { label: "大专", value: "大专" },
        { label: "本科", value: "本科" },
        { label: "硕士", value: "硕士" },
        { label: "博士", value: "博士" }
    ];

    // 工作时间偏好选项
    const workTimePreferenceOptions = [
        { label: "灵活", value: "flexible" },
        { label: "固定", value: "fixed" },
        { label: "兼职", value: "part_time" },
        { label: "全职", value: "full_time" }
    ];

    // 薪资类型偏好选项
    const salaryTypePreferenceOptions = [
        { label: "小时", value: "hourly" },
        { label: "日薪", value: "daily" },
        { label: "月薪", value: "monthly" },
        { label: "计件", value: "piece" }
    ];

    // 状态选项
    const statusOptions = [
        { label: "活跃", value: "active" },
        { label: "不活跃", value: "inactive" },
        { label: "暂停", value: "suspended" },
        { label: "禁用", value: "banned" }
    ];

    // 工作类别选项
    const workCategoryOptions = [
        { label: "服务员", value: "服务员" },
        { label: "保洁", value: "保洁" },
        { label: "搬运工", value: "搬运工" },
        { label: "销售", value: "销售" },
        { label: "客服", value: "客服" },
        { label: "配送员", value: "配送员" },
        { label: "厨师", value: "厨师" },
        { label: "司机", value: "司机" },
        { label: "保安", value: "保安" },
        { label: "其他", value: "其他" }
    ];

    // 工作类型偏好选项
    const jobTypePreferenceOptions = [
        { label: "全职", value: "全职" },
        { label: "兼职", value: "兼职" },
        { label: "临时工", value: "临时工" },
        { label: "小时工", value: "小时工" },
        { label: "实习", value: "实习" }
    ];

    return {
        dialogWidth: '1200px',  // 弹窗宽度
        dialogHeight: '70vh',   // 弹窗内容区最大高度
        labelWidth: '120px',
        column: [
            // ==================== 基本信息分组 ====================
            {
                label: "基本信息",
                prop: "divider_basic_info",
                formSlot: true,
                headerAlign: "left",
                labelWidth: 0,
                span: 24,
                divider: true
            },
            {
                label: "真实姓名",
                prop: "realName",
                search: true,
                searchSpan: 12,
                minWidth: 120,
                rules: [
                    { required: true, message: "真实姓名不能为空", trigger: "blur" },
                    { min: 2, max: 100, message: "真实姓名长度必须介于 2 和 100 之间", trigger: "blur" }
                ],
                span: 12
            },
            {
                label: "昵称",
                prop: "nickname",
                search: true,
                searchSpan: 12,
                minWidth: 120,
                span: 12,
                rules: [
                    { max: 100, message: "昵称不能超过100个字符", trigger: "blur" }
                ]
            },
            {
                label: "性别",
                prop: "gender",
                search: true,
                searchSpan: 12,
                width: 80,
                align: "center",
                type: "select",
                dicData: genderOptions,
                span: 8
            },
            {
                label: "年龄",
                prop: "age",
                search: false,
                width: 80,
                align: "center",
                type: "number",
                span: 8,
                min: 16,
                max: 80,
                rules: [
                    { type: 'number', min: 16, max: 80, message: '年龄必须在16-80之间', trigger: 'blur' }
                ]
            },
            {
                label: "出生日期",
                prop: "birthDate",
                search: false,
                type: "date",
                span: 8,
                format: "YYYY-MM-DD",
                valueFormat: "YYYY-MM-DD"
            },
            {
                label: "手机号",
                prop: "phone",
                search: true,
                searchSpan: 12,
                minWidth: 120,
                span: 12,
                rules: [
                    { required: true, message: "手机号不能为空", trigger: "blur" },
                    { pattern: /^1[3-9]\d{9}$/, message: "请输入正确的手机号码", trigger: "blur" }
                ]
            },
            {
                label: "邮箱",
                prop: "email",
                search: false,
                span: 12,
                rules: [
                    { type: 'email', message: '请输入正确的邮箱地址', trigger: 'blur' }
                ]
            },
            {
                label: "微信号",
                prop: "wechat",
                search: false,
                span: 12,
                rules: [
                    { max: 100, message: "微信号不能超过100个字符", trigger: "blur" }
                ]
            },
            {
                label: "身份证号",
                prop: "idCard",
                search: false,
                span: 12,
                rules: [
                    { pattern: /^[1-9]\d{5}(18|19|20)\d{2}((0[1-9])|(1[0-2]))(([0-2][1-9])|10|20|30|31)\d{3}[0-9Xx]$/, 
                      message: "请输入正确的身份证号", trigger: "blur" }
                ]
            },
            // ==================== 地址信息分组 ====================
            {
                label: "地址信息",
                prop: "divider_address_info",
                formSlot: true,
                headerAlign: "left",
                labelWidth: 0,
                span: 24,
                divider: true
            },
            {
                label: "当前所在地",
                prop: "currentLocation",
                search: true,
                searchSpan: 12,
                minWidth: 150,
                span: 12,
                rules: [
                    { max: 200, message: "当前所在地不能超过200个字符", trigger: "blur" }
                ]
            },
            {
                label: "详细地址",
                prop: "currentAddress",
                search: false,
                span: 12,
                rules: [
                    { max: 500, message: "详细地址不能超过500个字符", trigger: "blur" }
                ]
            },
            {
                label: "可工作地点",
                prop: "workLocations",
                search: false,
                type: "textarea",
                span: 24,
                minRows: 2,
                maxRows: 4,
                placeholder: "请输入可工作地点，多个地点用逗号分隔"
            },
            // ==================== 教育经验分组 ====================
            {
                label: "教育经验",
                prop: "divider_education_experience",
                formSlot: true,
                headerAlign: "left",
                labelWidth: 0,
                span: 24,
                divider: true
            },
            {
                label: "学历水平",
                prop: "educationLevel",
                search: true,
                searchSpan: 12,
                width: 100,
                align: "center",
                type: "select",
                dicData: educationLevelOptions,
                span: 12
            },
            {
                label: "工作经验年数",
                prop: "workExperienceYears",
                search: false,
                width: 120,
                align: "center",
                type: "number",
                span: 12,
                min: 0,
                max: 50,
                rules: [
                    { type: 'number', min: 0, max: 50, message: '工作经验年数必须在0-50之间', trigger: 'blur' }
                ]
            },
            {
                label: "工作类别偏好",
                prop: "workCategories",
                search: false,
                type: "textarea",
                span: 24,
                minRows: 2,
                maxRows: 4,
                placeholder: "请输入工作类别偏好，多个类别用逗号分隔"
            },
            {
                label: "工作类型偏好",
                prop: "jobTypesPreferred",
                search: false,
                type: "textarea",
                span: 24,
                minRows: 2,
                maxRows: 4,
                placeholder: "请输入工作类型偏好，多个类型用逗号分隔"
            },
            // ==================== 技能资质分组 ====================
            {
                label: "技能资质",
                prop: "divider_skills_certifications",
                formSlot: true,
                headerAlign: "left",
                labelWidth: 0,
                span: 24,
                divider: true
            },
            {
                label: "技能列表",
                prop: "skills",
                search: false,
                type: "textarea",
                span: 24,
                minRows: 3,
                maxRows: 6,
                placeholder: "请输入技能列表，多个技能用逗号分隔"
            },
            {
                label: "证书资质",
                prop: "certifications",
                search: false,
                type: "textarea",
                span: 24,
                minRows: 2,
                maxRows: 4,
                placeholder: "请输入证书资质，多个证书用逗号分隔"
            },
            {
                label: "语言能力",
                prop: "languages",
                search: false,
                type: "textarea",
                span: 24,
                minRows: 2,
                maxRows: 4,
                placeholder: "请输入语言能力，多个语言用逗号分隔"
            },
            // ==================== 工作偏好分组 ====================
            {
                label: "工作偏好",
                prop: "divider_work_preference",
                formSlot: true,
                headerAlign: "left",
                labelWidth: 0,
                span: 24,
                divider: true
            },
            {
                label: "工作时间偏好",
                prop: "workTimePreference",
                search: true,
                searchSpan: 12,
                width: 120,
                align: "center",
                type: "select",
                dicData: workTimePreferenceOptions,
                span: 12
            },
            {
                label: "薪资类型偏好",
                prop: "salaryTypePreference",
                search: false,
                width: 120,
                align: "center",
                type: "select",
                dicData: salaryTypePreferenceOptions,
                span: 12
            },
            {
                label: "期望最低薪资",
                prop: "salaryExpectationMin",
                search: false,
                type: "number",
                span: 12,
                precision: 2,
                min: 0,
                rules: [
                    { type: 'number', min: 0, message: '期望最低薪资不能小于0', trigger: 'blur' }
                ]
            },
            {
                label: "期望最高薪资",
                prop: "salaryExpectationMax",
                search: false,
                type: "number",
                span: 12,
                precision: 2,
                min: 0,
                rules: [
                    { type: 'number', min: 0, message: '期望最高薪资不能小于0', trigger: 'blur' }
                ]
            },
            // ==================== 可工作时间分组 ====================
            {
                label: "可工作时间",
                prop: "divider_availability",
                formSlot: true,
                headerAlign: "left",
                labelWidth: 0,
                span: 24,
                divider: true
            },
            {
                label: "可开始工作日期",
                prop: "availabilityStartDate",
                search: false,
                type: "date",
                span: 12,
                format: "YYYY-MM-DD",
                valueFormat: "YYYY-MM-DD"
            },
            {
                label: "可工作截止日期",
                prop: "availabilityEndDate",
                search: false,
                type: "date",
                span: 12,
                format: "YYYY-MM-DD",
                valueFormat: "YYYY-MM-DD"
            },
            {
                label: "每周可工作天数",
                prop: "workDaysPerWeek",
                search: false,
                type: "number",
                span: 12,
                min: 1,
                max: 7,
                rules: [
                    { type: 'number', min: 1, max: 7, message: '每周可工作天数必须在1-7之间', trigger: 'blur' }
                ]
            },
            {
                label: "每日可工作小时",
                prop: "workHoursPerDay",
                search: false,
                type: "number",
                span: 12,
                min: 1,
                max: 24,
                rules: [
                    { type: 'number', min: 1, max: 24, message: '每日可工作小时必须在1-24之间', trigger: 'blur' }
                ]
            },
            // ==================== 健康状况分组 ====================
            {
                label: "健康状况",
                prop: "divider_health_status",
                formSlot: true,
                headerAlign: "left",
                labelWidth: 0,
                span: 24,
                divider: true
            },
            {
                label: "身体状况",
                prop: "physicalCondition",
                search: false,
                span: 12,
                rules: [
                    { max: 100, message: "身体状况不能超过100个字符", trigger: "blur" }
                ]
            },
            {
                label: "健康证",
                prop: "healthCertificate",
                search: true,
                searchSpan: 12,
                width: 100,
                align: "center",
                type: "switch",
                dicData: [
                    { label: "有", value: 1 },
                    { label: "无", value: 0 }
                ],
                span: 6
            },
            {
                label: "无犯罪记录",
                prop: "criminalRecordCheck",
                search: true,
                searchSpan: 12,
                width: 120,
                align: "center",
                type: "switch",
                dicData: [
                    { label: "是", value: 1 },
                    { label: "否", value: 0 }
                ],
                span: 6
            },
            // ==================== 紧急联系人分组 ====================
            {
                label: "紧急联系人",
                prop: "divider_emergency_contact",
                formSlot: true,
                headerAlign: "left",
                labelWidth: 0,
                span: 24,
                divider: true
            },
            {
                label: "联系人姓名",
                prop: "emergencyContactName",
                search: false,
                span: 8,
                rules: [
                    { max: 100, message: "联系人姓名不能超过100个字符", trigger: "blur" }
                ]
            },
            {
                label: "联系人电话",
                prop: "emergencyContactPhone",
                search: false,
                span: 8,
                rules: [
                    { pattern: /^1[3-9]\d{9}$/, message: "请输入正确的手机号码", trigger: "blur" }
                ]
            },
            {
                label: "联系人关系",
                prop: "emergencyContactRelation",
                search: false,
                span: 8,
                rules: [
                    { max: 50, message: "联系人关系不能超过50个字符", trigger: "blur" }
                ]
            },
            // ==================== 个人介绍分组 ====================
            {
                label: "个人介绍",
                prop: "divider_personal_intro",
                formSlot: true,
                headerAlign: "left",
                labelWidth: 0,
                span: 24,
                divider: true
            },
            {
                label: "自我介绍",
                prop: "selfIntroduction",
                search: false,
                type: "textarea",
                span: 24,
                minRows: 4,
                maxRows: 8,
                showWordLimit: true,
                maxlength: 1000
            },
            {
                label: "特殊说明",
                prop: "specialNotes",
                search: false,
                type: "textarea",
                span: 24,
                minRows: 2,
                maxRows: 4,
                showWordLimit: true,
                maxlength: 500
            },
            // ==================== 统计信息分组 ====================
            {
                label: "统计信息",
                prop: "divider_statistics",
                formSlot: true,
                headerAlign: "left",
                labelWidth: 0,
                span: 24,
                divider: true,
                addDisplay: false,
                editDisplay: false
            },
            {
                label: "平均评分",
                prop: "ratingAverage",
                search: false,
                width: 100,
                align: "center",
                span: 6,
                addDisplay: false,
                editDisplay: false,
                formatter: (row, column, cellValue) => {
                    return cellValue ? cellValue.toFixed(2) : '0.00'
                }
            },
            {
                label: "评分次数",
                prop: "ratingCount",
                search: false,
                width: 100,
                align: "center",
                span: 6,
                addDisplay: false,
                editDisplay: false
            },
            {
                label: "完成工作数",
                prop: "completedJobs",
                search: false,
                width: 120,
                align: "center",
                span: 6,
                addDisplay: false,
                editDisplay: false
            },
            {
                label: "成功率",
                prop: "successRate",
                search: false,
                width: 100,
                align: "center",
                span: 6,
                addDisplay: false,
                editDisplay: false,
                formatter: (row, column, cellValue) => {
                    return cellValue ? cellValue.toFixed(2) + '%' : '0.00%'
                }
            },
            // ==================== 状态信息分组 ====================
            {
                label: "状态信息",
                prop: "divider_status_info",
                formSlot: true,
                headerAlign: "left",
                labelWidth: 0,
                span: 24,
                divider: true,
                addDisplay: false,
                editDisplay: false
            },
            {
                label: "状态",
                prop: "status",
                search: true,
                searchSpan: 12,
                width: 100,
                align: "center",
                type: "select",
                dicData: statusOptions,
                span: 8,
                slot: true,
                addDisplay: false,
                editDisplay: false
            },
            {
                label: "是否验证",
                prop: "isVerified",
                search: true,
                searchSpan: 12,
                width: 100,
                align: "center",
                type: "switch",
                dicData: [
                    { label: "是", value: 1 },
                    { label: "否", value: 0 }
                ],
                span: 8,
                addDisplay: false,
                editDisplay: false
            },
            {
                label: "用户名",
                prop: "userName",
                search: true,
                searchSpan: 12,
                minWidth: 120,
                span: 8,
                addDisplay: false,
                editDisplay: false
            },
            {
                label: "创建时间",
                prop: "createTime",
                search: true,
                searchSpan: 12,
                searchType: "daterange",
                searchFormat: "YYYY-MM-DD",
                searchValueFormat: "YYYY-MM-DD",
                width: 180,
                align: "center",
                formatter: (row, column, cellValue) => {
                    return parseTime(cellValue, '{y}-{m}-{d} {h}:{i}:{s}')
                },
                span: 12,
                addDisplay: false,
                editDisplay: false
            },
            {
                label: "验证时间",
                prop: "verificationTime",
                search: false,
                width: 180,
                align: "center",
                formatter: (row, column, cellValue) => {
                    return cellValue ? parseTime(cellValue, '{y}-{m}-{d} {h}:{i}:{s}') : '-'
                },
                span: 12,
                addDisplay: false,
                editDisplay: false
            },
            {
                label: "最后活跃时间",
                prop: "lastActiveTime",
                search: false,
                width: 180,
                align: "center",
                formatter: (row, column, cellValue) => {
                    return cellValue ? parseTime(cellValue, '{y}-{m}-{d} {h}:{i}:{s}') : '-'
                },
                span: 12,
                addDisplay: false,
                editDisplay: false
            },
            {
                label: "备注",
                prop: "remark",
                search: false,
                type: "textarea",
                span: 24,
                minRows: 2,
                maxRows: 4,
                showWordLimit: true,
                maxlength: 500
            }
        ]
    };
};
