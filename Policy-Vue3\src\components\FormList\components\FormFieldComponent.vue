<template>
  <div class="form-field-component">

    <!-- 自定义渲染，使用插槽 -->
    <slot v-if="field.formSlot" :name="field.prop" :field="field" :value="innerValue" :disabled="disabled">
    </slot>

    <!-- 输入框 -->
    <el-input v-else-if="field.type === 'input' || !field.type" v-model="innerValue" :placeholder="getPlaceholder()"
      :disabled="disabled" :maxlength="field.maxlength" :show-word-limit="field.showWordLimit"
      :clearable="field.clearable !== false" class="modern-input" />

    <!-- 密码框 -->
    <el-input v-else-if="field.type === 'password'" v-model="innerValue" type="password" :placeholder="getPlaceholder()"
      :disabled="disabled" :maxlength="field.maxlength" :show-password="true" :clearable="field.clearable !== false"
      class="modern-input" />

    <!-- 文本域 -->
    <el-input v-else-if="field.type === 'textarea'" v-model="innerValue" type="textarea" :placeholder="getPlaceholder()"
      :disabled="disabled" :maxlength="field.maxlength" :show-word-limit="field.showWordLimit" :rows="field.rows || 3"
      :autosize="field.autosize" class="modern-textarea" />

    <!-- 数字输入框 -->
    <el-input-number v-else-if="field.type === 'number'" v-model="innerValue" :placeholder="getPlaceholder()"
      :disabled="disabled" :min="field.min" :max="field.max" :step="field.step" :precision="field.precision"
      :controls="field.controls !== false" class="modern-number full-width" />

    <!-- 数字输入框(带后缀) -->
    <div v-else-if="field.type === 'number-suffix'" class="number-suffix-wrapper">
      <el-input-number v-model="innerValue" :placeholder="getPlaceholder()" :disabled="disabled" :min="field.min"
        :max="field.max" :step="field.step" :precision="field.precision" :controls="field.controls !== false"
        class="modern-number" />
      <span class="suffix-icon" v-if="field.suffix">{{ field.suffix }}</span>
    </div>

    <!-- Api选择器 -->
    <el-select v-else-if="field.type === 'api-select'" v-model="innerValue" :placeholder="getPlaceholder()"
      :disabled="disabled" :clearable="field.clearable !== false" :multiple="field.multiple"
      :collapse-tags="field.collapseTags" :filterable="field.filterable" class="modern-select full-width">
      <el-option v-for="item in field.dicData" :key="item.value" :label="item.label" :value="item.value"
        :disabled="item.disabled" />
    </el-select>

    <!-- 远程搜索 -->
    <el-select v-else-if="field.type === 'remote-search'" v-model="innerValue" :placeholder="getPlaceholder()"
      :disabled="disabled" :clearable="field.clearable !== false" :filterable="true" remote
      :remote-method="handleRemoteSearch" :loading="remoteLoading" class="modern-select full-width">
      <el-option v-for="item in remoteOptions" :key="item.value" :label="item.label" :value="item.value" />
    </el-select>

    <!-- Api树选择器 -->
    <el-tree-select v-else-if="field.type === 'api-tree-select'" v-model="innerValue" :data="normalizedTreeData"
      :placeholder="getPlaceholder()" :disabled="disabled" :clearable="field.clearable !== false"
      :check-strictly="field.checkStrictly" :multiple="field.multiple" class="modern-tree-select full-width" />

    <!-- 下拉选择框 -->
    <el-select v-else-if="field.type === 'select'" v-model="innerValue" :placeholder="getPlaceholder()"
      :disabled="disabled" :clearable="field.clearable !== false" :multiple="field.multiple"
      :collapse-tags="field.collapseTags" :filterable="field.filterable" class="modern-select full-width">
      <el-option v-for="item in field.dicData" :key="item.value" :label="item.label" :value="item.value"
        :disabled="item.disabled" />
    </el-select>

    <!-- 单选框组 -->
    <el-radio-group v-else-if="field.type === 'radio'" v-model="innerValue" :disabled="disabled" :size="field.size"
      class="modern-radio-group">
      <template v-if="field.button">
        <el-radio-button v-for="item in field.dicData" :key="item.value" :label="item.label" :disabled="item.disabled"
          :value="item.value" class="modern-radio-button">
        </el-radio-button>
      </template>
      <template v-else>
        <el-radio v-for="item in field.dicData" :key="item.value" :label="item.label" :disabled="item.disabled"
          :value="item.value" class="modern-radio">
        </el-radio>
      </template>
    </el-radio-group>

    <!-- 多选框组 -->
    <el-checkbox-group v-else-if="field.type === 'checkbox'" v-model="innerValue" :disabled="disabled"
      :size="field.size" class="modern-checkbox-group">
      <template v-if="field.button">
        <el-checkbox-button v-for="item in field.dicData" :key="item.value" :value="item.value"
          :disabled="item.disabled" class="modern-checkbox-button">
          {{ item.label }}
        </el-checkbox-button>
      </template>
      <template v-else>
        <el-checkbox v-for="item in field.dicData" :key="item.value" :value="item.value" :disabled="item.disabled"
          class="modern-checkbox">
          {{ item.label }}
        </el-checkbox>
      </template>
    </el-checkbox-group>

    <!-- 开关 -->
    <el-switch v-else-if="field.type === 'switch'" v-model="innerValue" :disabled="disabled"
      :active-value="field.activeValue !== undefined ? field.activeValue : true"
      :inactive-value="field.inactiveValue !== undefined ? field.inactiveValue : false" :active-text="field.activeText"
      :inactive-text="field.inactiveText" class="modern-switch" />

    <!-- 滑块 -->
    <el-slider v-else-if="field.type === 'slider'" v-model="innerValue" :disabled="disabled" :min="field.min"
      :max="field.max" :step="field.step" :show-input="field.showInput" :range="field.range" class="modern-slider" />

    <!-- 日期选择器 -->
    <el-date-picker v-else-if="field.type === 'date'" v-model="innerValue" type="date" :placeholder="getPlaceholder()"
      :disabled="disabled" :clearable="field.clearable !== false" :format="field.format || 'YYYY-MM-DD'"
      :value-format="field.valueFormat || 'YYYY-MM-DD'" class="modern-date-picker full-width" />

    <!-- 日期时间选择器 -->
    <el-date-picker v-else-if="field.type === 'datetime'" v-model="innerValue" type="datetime"
      :placeholder="getPlaceholder()" :disabled="disabled" :clearable="field.clearable !== false"
      :format="field.format || 'YYYY-MM-DD HH:mm:ss'" :value-format="field.valueFormat || 'YYYY-MM-DD HH:mm:ss'"
      class="modern-date-picker full-width" />

    <!-- 时间选择器 -->
    <el-time-picker v-else-if="field.type === 'time'" v-model="innerValue" :placeholder="getPlaceholder()"
      :disabled="disabled" :clearable="field.clearable !== false" :format="field.format || 'HH:mm:ss'"
      :value-format="field.valueFormat || 'HH:mm:ss'" class="modern-time-picker full-width" />

    <!-- 日期范围选择器 -->
    <el-date-picker v-else-if="field.type === 'daterange'" v-model="innerValue" type="daterange" range-separator="至"
      start-placeholder="开始日期" end-placeholder="结束日期" :disabled="disabled" :clearable="field.clearable !== false"
      :format="field.format || 'YYYY-MM-DD'" :value-format="field.valueFormat || 'YYYY-MM-DD'"
      class="modern-date-picker full-width" />

    <!-- 时间范围选择器 -->
    <el-time-picker v-else-if="field.type === 'timerange'" v-model="innerValue" is-range range-separator="至"
      start-placeholder="开始时间" end-placeholder="结束时间" :disabled="disabled" :clearable="field.clearable !== false"
      :format="field.format || 'HH:mm:ss'" :value-format="field.valueFormat || 'HH:mm:ss'"
      class="modern-time-picker full-width" />

    <!-- 颜色选择器 -->
    <el-color-picker v-else-if="field.type === 'color'" v-model="innerValue" :disabled="disabled"
      :show-alpha="field.showAlpha" class="modern-color-picker" />

    <!-- 评分 -->
    <el-rate v-else-if="field.type === 'rate'" v-model="innerValue" :disabled="disabled" :max="field.max || 5"
      :allow-half="field.allowHalf" class="modern-rate" />

    <!-- 级联选择器 -->
    <el-cascader v-else-if="field.type === 'cascader'" v-model="innerValue" :options="field.dicData"
      :props="field.props || { checkStrictly: false }" :placeholder="getPlaceholder()" :disabled="disabled"
      :clearable="field.clearable !== false" :filterable="field.filterable" class="modern-cascader full-width" />

    <!-- 树选择器 -->
    <el-tree-select v-else-if="field.type === 'tree-select'" v-model="innerValue" :data="normalizedTreeData"
      :placeholder="getPlaceholder()" :disabled="disabled" :clearable="field.clearable !== false"
      :check-strictly="field.checkStrictly" :multiple="field.multiple" class="modern-tree-select full-width" />

    <!-- 图标选择器 -->
    <div v-else-if="field.type === 'icon'" class="icon-selector-wrapper">
      <el-input v-model="innerValue" :placeholder="getPlaceholder()" :disabled="disabled" readonly
        class="modern-input icon-input" @click="showIconSelector = true">
        <template #prefix>
          <i :class="innerValue" v-if="innerValue"></i>
          <el-icon v-else>
            <Grid />
          </el-icon>
        </template>
        <template #suffix>
          <el-icon class="icon-arrow">
            <ArrowDown />
          </el-icon>
        </template>
      </el-input>
      <!-- 图标选择器弹窗可以在这里实现 -->
    </div>

    <!-- 提及输入框 -->
    <el-input v-else-if="field.type === 'mention'" v-model="innerValue" :placeholder="getPlaceholder()"
      :disabled="disabled" :maxlength="field.maxlength" :clearable="field.clearable !== false"
      class="modern-input mention-input">
      <template #prefix>
        <span class="mention-prefix">@</span>
      </template>
    </el-input>

    <!-- 文件上传 -->
    <div v-else-if="field.type === 'file'" class="file-upload-wrapper">
      <el-upload :disabled="disabled" :action="field.action || '#'" :before-upload="handleBeforeUpload"
        :on-success="handleUploadSuccess" :on-error="handleUploadError" :file-list="fileList"
        :list-type="field.listType || 'text'" :accept="field.accept" :multiple="field.multiple" class="modern-upload">
        <template #trigger>
          <el-button type="primary" class="upload-btn">
            <el-icon>
              <UploadFilled />
            </el-icon>
            {{ field.uploadText || '点击上传图片' }}
          </el-button>
        </template>
        <template #tip>
          <div class="el-upload__tip" v-if="field.tip">
            {{ field.tip }}
          </div>
        </template>
      </el-upload>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import { Grid, ArrowDown, UploadFilled } from '@element-plus/icons-vue';

const props = defineProps({
  field: {
    type: Object,
    required: true
  },
  modelValue: {
    type: [String, Number, Boolean, Array, Object],
    default: null
  },
  disabled: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:modelValue']);

// 内部值，用于双向绑定
const innerValue = computed({
  get: () => {
    // 处理特殊类型的默认值
    if (props.modelValue === undefined || props.modelValue === null) {
      if (props.field.type === 'checkbox') return [];
      if (props.field.type === 'switch') return props.field.inactiveValue !== undefined ? props.field.inactiveValue : false;
      if (props.field.type === 'number' || props.field.type === 'number-suffix') return props.field.min || 0;
      if (props.field.type === 'rate') return props.field.defaultValue !== undefined ? props.field.defaultValue : 0;
      if (props.field.type === 'slider') return props.field.defaultValue !== undefined ? props.field.defaultValue : props.field.min || 0;
      return props.field.defaultValue !== undefined ? props.field.defaultValue : '';
    }
    return props.modelValue;
  },
  set: (val) => {
    // 避免重复设置相同的值导致递归更新
    if (JSON.stringify(val) === JSON.stringify(props.modelValue)) {
      return;
    }
    emit('update:modelValue', val);
  }
});

// 标准化树形数据，确保每个节点都有必要的属性
const normalizedTreeData = computed(() => {
  if (!props.field.dicData || !Array.isArray(props.field.dicData)) {
    return [];
  }

  const normalizeNode = (node) => {
    if (!node || typeof node !== 'object') {
      return null;
    }
    
    const normalized = {
      ...node,
      // 确保有value属性，如果没有则使用id
      value: node.value !== undefined ? node.value : node.id,
      // 确保有label属性
      label: node.label || node.name || node.title || String(node.id),
    };

    // 递归处理子节点
    if (node.children && Array.isArray(node.children)) {
      normalized.children = node.children
        .map(normalizeNode)
        .filter(child => child !== null);
    }

    return normalized;
  };

  return props.field.dicData
    .map(normalizeNode)
    .filter(node => node !== null);
});

// 远程搜索相关状态
const remoteLoading = ref(false);
const remoteOptions = ref([]);
const showIconSelector = ref(false);
const fileList = ref([]);

// 获取占位符文本
const getPlaceholder = () => {
  if (props.field.placeholder) return props.field.placeholder;

  // 生成默认占位符
  const prefix = props.field.type === 'select' || props.field.type === 'cascader' ||
    props.field.type === 'tree-select' || props.field.type === 'api-select' ||
    props.field.type === 'api-tree-select' || props.field.type === 'remote-search' ? '请选择' : '请输入';
  return `${prefix}${props.field.label || ''}`;
};

// 远程搜索方法
const handleRemoteSearch = (query) => {
  if (query && typeof props.field.remoteMethod === 'function') {
    remoteLoading.value = true;
    props.field.remoteMethod(query).then(options => {
      remoteOptions.value = options;
      remoteLoading.value = false;
    }).catch(() => {
      remoteLoading.value = false;
    });
  }
};

// 文件上传处理
const handleBeforeUpload = (file) => {
  if (props.field.beforeUpload && typeof props.field.beforeUpload === 'function') {
    return props.field.beforeUpload(file);
  }
  return true;
};

const handleUploadSuccess = (response, file) => {
  if (props.field.onSuccess && typeof props.field.onSuccess === 'function') {
    props.field.onSuccess(response, file);
  }
  // 更新文件列表
  fileList.value.push({
    name: file.name,
    url: response.url || response.data?.url
  });
  emit('update:modelValue', fileList.value);
};

const handleUploadError = (error, file) => {
  if (props.field.onError && typeof props.field.onError === 'function') {
    props.field.onError(error, file);
  }
};
</script>

<style lang="scss" scoped>
.form-field-component {
  width: 100%;
  
  // CSS 变量定义
  --el-input-number-button-width: 32px;
  --el-input-number-button-height: 16px;
  --el-input-number-button-hover-bg: #f5f7fa;
  --el-input-number-button-active-bg: #e6f7ff;

  // 确保所有表单组件占满整个容器宽度并统一高度
  .full-width,
  .modern-input,
  .modern-number,
  .modern-select,
  .modern-tree-select,
  .modern-cascader,
  .modern-date-picker,
  .modern-time-picker {
    width: 100% !important;
    height: 32px !important;
  }

  // 文本域保持自适应高度
  .modern-textarea {
    width: 100% !important;
  }

  // 数字输入框特殊处理
  .modern-number,
  .modern-number.full-width {
    width: 100% !important;
    
    :deep(.el-input-number) {
      width: 100%;
      height: 32px;
      
      .el-input {
        height: 32px;
        
        .el-input__wrapper {
          height: 32px;
        }
      }
      
      .el-input-number__decrease,
      .el-input-number__increase {
        width: var(--el-input-number-button-width);
        height: var(--el-input-number-button-height);
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 0;
        transition: all 0.2s ease;
        cursor: pointer;
        
        &:hover:not(.is-disabled) {
          background-color: var(--el-input-number-button-hover-bg);
          color: #409eff;
        }
        
        &:active:not(.is-disabled) {
          background-color: var(--el-input-number-button-active-bg);
        }
        
        .el-icon {
          font-size: 12px;
          transition: color 0.2s ease;
        }
      }
      
      .el-input-number__decrease {
        border-bottom-left-radius: 6px;
        border-right: 1px solid var(--el-border-color, #dcdfe6);
        border-bottom: none;
        
        &:hover:not(.is-disabled) .el-icon {
          transform: scale(1.1);
        }
      }
      
      .el-input-number__increase {
        border-top-left-radius: 6px;
        border-right: 1px solid var(--el-border-color, #dcdfe6);
        border-top: none;
        
        &:hover:not(.is-disabled) .el-icon {
          transform: scale(1.1);
        }
      }
    }
  }

  // 数字输入框后缀容器
  .number-suffix-wrapper {
    display: flex;
    align-items: center;
    width: 100%;
    height: 32px;
    
    .modern-number {
      flex: 1;
      margin-right: 8px;
      min-width: 120px;
      
      :deep(.el-input-number) {
        width: 100%;
        height: 32px;
        
        .el-input {
          height: 32px;
          
          .el-input__wrapper {
            height: 32px;
          }
        }
        
        .el-input-number__decrease,
        .el-input-number__increase {
          width: 30px;
          height: var(--el-input-number-button-height);
          display: flex;
          align-items: center;
          justify-content: center;
          border-radius: 0;
          transition: all 0.2s ease;
          cursor: pointer;
          
          &:hover:not(.is-disabled) {
            background-color: var(--el-input-number-button-hover-bg);
            color: #409eff;
          }
          
          &:active:not(.is-disabled) {
            background-color: var(--el-input-number-button-active-bg);
          }
          
          .el-icon {
            font-size: 12px;
            transition: color 0.2s ease;
          }
        }
        
        .el-input-number__decrease {
          border-bottom-left-radius: 6px;
          border-right: 1px solid var(--el-border-color, #dcdfe6);
          border-bottom: none;
          
          &:hover:not(.is-disabled) .el-icon {
            transform: scale(1.1);
          }
        }
        
        .el-input-number__increase {
          border-top-left-radius: 6px;
          border-right: 1px solid var(--el-border-color, #dcdfe6);
          border-top: none;
          
          &:hover:not(.is-disabled) .el-icon {
            transform: scale(1.1);
          }
        }
      }
    }
    
    .suffix-icon {
      color: #606266;
      font-size: 14px;
      white-space: nowrap;
      line-height: 32px;
      padding-left: 4px;
      flex-shrink: 0;
    }
  }

  // 选择器样式
  .modern-select,
  .modern-tree-select,
  .modern-cascader {
    :deep(.el-select),
    :deep(.el-tree-select),
    :deep(.el-cascader) {
      width: 100%;
    }
  }

  // 日期时间选择器
  .modern-date-picker,
  .modern-time-picker {
    :deep(.el-date-editor) {
      width: 100%;
    }
  }

  // 单选框组和多选框组保持自然布局
  .modern-radio-group,
  .modern-checkbox-group {
    width: 100%;
    min-height: 32px;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    .modern-radio,
    .modern-checkbox {
      margin-right: 16px;
      margin-bottom: 4px;
      height: 32px;
      display: flex;
      align-items: center;
    }
    
    .modern-radio-button,
    .modern-checkbox-button {
      margin-bottom: 4px;
      height: 32px;
      display: flex;
      align-items: center;
    }
  }

  // 开关保持左对齐
  .modern-switch {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    height: 32px;
  }

  // 滑块
  .modern-slider {
    width: 100%;
    height: 32px;
    display: flex;
    align-items: center;
  }

  // 颜色选择器
  .modern-color-picker {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    height: 32px;
  }

  // 评分
  .modern-rate {
    display: flex;
    justify-content: flex-start;
    align-items: center;
    height: 32px;
  }

  // 图标选择器
  .icon-selector-wrapper {
    width: 100%;
    height: 32px;
    
    .icon-input {
      cursor: pointer;
      height: 32px;
      
      :deep(.el-input__wrapper) {
        cursor: pointer;
        height: 32px;
      }
      
      .icon-arrow {
        transition: transform 0.3s;
      }
    }
  }

  // 提及输入框
  .mention-input {
    height: 32px;
    
    :deep(.el-input__wrapper) {
      height: 32px;
    }
    
    .mention-prefix {
      color: #409eff;
      font-weight: 600;
      line-height: 32px;
    }
  }

  // 文件上传
  .file-upload-wrapper {
    width: 100%;
    
    .modern-upload {
      width: 100%;
      
      .upload-btn {
        background: linear-gradient(135deg, #409eff 0%, #66b3ff 100%);
        border: none;
        border-radius: 6px;
        padding: 8px 16px;
        color: white;
        font-weight: 500;
        transition: all 0.3s ease;
        
        &:hover {
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(64, 158, 255, 0.3);
        }
      }
      
      :deep(.el-upload) {
        width: 100%;
      }
      
      :deep(.el-upload__tip) {
        margin-top: 8px;
        color: #909399;
        font-size: 12px;
      }
    }
  }

  // 统一的现代化样式
  .modern-input,
  .modern-textarea {
    :deep(.el-input__wrapper) {
      border-radius: 6px;
      transition: all 0.3s ease;
      height: 32px;
      
      &:hover {
        border-color: #409eff;
      }
      
      &.is-focus {
        border-color: #409eff;
      }
    }
  }

  // 文本域特殊处理，保持自适应高度
  .modern-textarea {
    :deep(.el-input__wrapper) {
      height: auto !important;
      min-height: 64px;
    }
  }

  .modern-select {
    :deep(.el-select__wrapper) {
      border-radius: 6px;
      transition: all 0.3s ease;
      height: 32px;
      
      &:hover {
        border-color: #409eff;
      }
      
      &.is-focus {
        border-color: #409eff;
        box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
      }
    }
  }

  .modern-tree-select {
    :deep(.el-tree-select__wrapper) {
      border-radius: 6px;
      transition: all 0.3s ease;
      height: 32px;
      
      &:hover {
        border-color: #409eff;
      }
      
      &.is-focus {
        border-color: #409eff;
        box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
      }
    }
  }

  .modern-cascader {
    :deep(.el-cascader) {
      border-radius: 6px;
      transition: all 0.3s ease;
      height: 32px;
      
      &:hover {
        border-color: #409eff;
      }
      
      &.is-focus {
        border-color: #409eff;
        box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
      }
    }
  }

  .modern-date-picker,
  .modern-time-picker {
    :deep(.el-date-editor) {
      border-radius: 6px;
      transition: all 0.3s ease;
      height: 32px;
      
      &:hover {
        border-color: #409eff;
      }
      
      &.is-focus {
        border-color: #409eff;
        box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
      }
    }
  }

  .modern-number {
    :deep(.el-input-number) {
      border-radius: 6px;
      height: 32px;
      
      .el-input {
        height: 32px;
        
        .el-input__wrapper {
          border-radius: 6px;
          transition: all 0.3s ease;
          height: 32px;
          
          &:hover {
            border-color: #409eff;
          }
          
          &.is-focus {
            border-color: #409eff;
            box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.1);
          }
        }
      }
      
      .el-input-number__decrease,
      .el-input-number__increase {
        width: var(--el-input-number-button-width);
        height: var(--el-input-number-button-height);
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 0;
        transition: all 0.2s ease;
        cursor: pointer;
        
        &:hover:not(.is-disabled) {
          background-color: var(--el-input-number-button-hover-bg);
          color: #409eff;
        }
        
        &:active:not(.is-disabled) {
          background-color: var(--el-input-number-button-active-bg);
        }
        
        .el-icon {
          font-size: 12px;
          transition: color 0.2s ease;
        }
      }
      
      .el-input-number__decrease {
        border-bottom-left-radius: 6px;
        border-right: 1px solid var(--el-border-color, #dcdfe6);
        border-bottom: none;
        
        &:hover:not(.is-disabled) .el-icon {
          transform: scale(1.1);
        }
      }
      
      .el-input-number__increase {
        border-top-left-radius: 6px;
        border-right: 1px solid var(--el-border-color, #dcdfe6);
        border-top: none;
        
        &:hover:not(.is-disabled) .el-icon {
          transform: scale(1.1);
        }
      }
      
      &.is-disabled {
        .el-input-number__decrease,
        .el-input-number__increase {
          background-color: #f5f7fa;
          color: #c0c4cc;
          cursor: not-allowed;
          
          &:hover {
            background-color: #f5f7fa;
            color: #c0c4cc;
          }
        }
      }
    }
  }

  // 响应式设计
  @media (max-width: 768px) {
    .modern-radio-group,
    .modern-checkbox-group {
      flex-direction: column;
      
      .modern-radio,
      .modern-checkbox {
        margin-right: 0;
        margin-bottom: 8px;
      }
    }
    
    // 移动端数字输入框优化
    --el-input-number-button-width: 28px;
    --el-input-number-button-height: 15px;
    
    .number-suffix-wrapper {
      .modern-number {
        min-width: 100px;
        
        :deep(.el-input-number) {
          .el-input-number__decrease,
          .el-input-number__increase {
            width: var(--el-input-number-button-width);
            height: var(--el-input-number-button-height);
            
            .el-icon {
              font-size: 10px;
            }
          }
        }
      }
      
      .suffix-icon {
        font-size: 12px;
      }
    }
    
    .modern-number {
      :deep(.el-input-number) {
        .el-input-number__decrease,
        .el-input-number__increase {
          width: var(--el-input-number-button-width);
          height: var(--el-input-number-button-height);
          
          .el-icon {
            font-size: 10px;
          }
        }
      }
    }
  }
  
  // 解决数字输入框在某些情况下的显示问题
  :deep(.el-form-item__content) {
    .modern-number {
      .el-input-number {
        line-height: 1;
        
        .el-input__inner {
          text-align: center;
          padding-left: 35px;
          padding-right: 8px;
        }
      }
    }
    
    .number-suffix-wrapper {
      .modern-number {
        .el-input-number {
          .el-input__inner {
            padding-left: 33px;
            padding-right: 8px;
          }
        }
      }
    }
  }
}
</style>